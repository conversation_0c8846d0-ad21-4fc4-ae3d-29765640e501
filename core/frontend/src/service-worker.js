importScripts('https://gcore.jsdelivr.net/npm/workbox-sw@7.1.0/build/workbox-sw.min.js'); 

import { precacheAndRoute } from 'workbox-precaching/precacheAndRoute';
precacheAndRoute(self.__WB_MANIFEST);

// 判断workbox是否加载成功
if (workbox) {
  console.log(`Yay! Workbox is loaded `);

  workbox.core.setCacheNameDetails({
    prefix: "FET",
    suffix: process.env.VUE_APP_VERSION
  });
  workbox.core.skipWaiting(); // 强制等待中的 Service Worker 被激活
  workbox.core.clientsClaim(); // Service Worker 被激活后使其立即获得页面控制权
  workbox.precaching.precacheAndRoute(self.__precacheManifest || []); // 设置预加载
  
  // 缓存web的css资源
  workbox.routing.registerRoute(
    // Cache CSS files
    /.*\.css/,
    // 使用缓存，但尽快在后台更新
    new workbox.strategies.StaleWhileRevalidate({
      // 使用自定义缓存名称
      cacheName: "css-cache",
      plugins: [
        new workbox.expiration.ExpirationPlugin({
          maxEntries: 100,
          maxAgeSeconds: 7 * 24 * 60 * 60 // 7天
        })
      ]
    })
  );

  // 缓存web的js资源
  workbox.routing.registerRoute(
    // 缓存JS文件
    /.*\.js/,
    // 使用缓存，但尽快在后台更新
    new workbox.strategies.StaleWhileRevalidate({
      // 使用自定义缓存名称
      cacheName: "js-cache",
      plugins: [
        new workbox.expiration.ExpirationPlugin({
          maxEntries: 100,
          maxAgeSeconds: 7 * 24 * 60 * 60 // 7天
        })
      ]
    })
  );
  
  // 缓存web的图片资源
  workbox.routing.registerRoute(
    /\.(?:png|gif|jpg|jpeg|svg)$/,
    new workbox.strategies.StaleWhileRevalidate({
      cacheName: "images",
      plugins: [
        new workbox.expiration.ExpirationPlugin({
          maxEntries: 60,
          maxAgeSeconds: 30 * 24 * 60 * 60 // 设置缓存有效期为30天
        })
      ]
    })
  );
  
  // 我们很多资源在其他域名上，比如cdn、oss等，这里做单独处理，需要支持跨域
  workbox.routing.registerRoute(
    /^https:\/\/image\.finevent\.top\/.*\.(jpe?g|png|gif|svg)/,
    new workbox.strategies.StaleWhileRevalidate({
      cacheName: "cdn-images",
      plugins: [
        new workbox.expiration.ExpirationPlugin({
          maxEntries: 60,
          maxAgeSeconds: 10 * 24 * 60 * 60 // 设置缓存有效期为10天
        })
      ],
      fetchOptions: {
        credentials: "include" // 支持跨域
      }
    })
  );
  
  // 我们很多资源在其他域名上，比如cdn、oss等，这里做单独处理，需要支持跨域
  workbox.routing.registerRoute(
    /^https:\/\/assets\.finevent\.top\/.*\.(js|css)/,
    new workbox.strategies.StaleWhileRevalidate({
      cacheName: "oss-file",
      plugins: [
        new workbox.expiration.ExpirationPlugin({
          maxEntries: 60,
          maxAgeSeconds: 10 * 24 * 60 * 60 // 设置缓存有效期为10天
        })
      ],
      fetchOptions: {
        credentials: "include" // 支持跨域
      }
    })
  );

  // 股票网站禁用API缓存，确保数据实时性
  // workbox.routing.registerRoute(
  //   /^https:\/\/.*\/api\//,
  //   new workbox.strategies.NetworkFirst({
  //     cacheName: "api-cache",
  //     networkTimeoutSeconds: 3,
  //     plugins: [
  //       new workbox.expiration.ExpirationPlugin({
  //         maxEntries: 50,
  //         maxAgeSeconds: 5 * 60 // 5分钟
  //       })
  //     ]
  //   })
  // );

  // 缓存字体文件
  workbox.routing.registerRoute(
    /\.(?:woff|woff2|ttf|eot)$/,
    new workbox.strategies.CacheFirst({
      cacheName: "fonts-cache",
      plugins: [
        new workbox.expiration.ExpirationPlugin({
          maxEntries: 30,
          maxAgeSeconds: 365 * 24 * 60 * 60 // 1年
        })
      ]
    })
  );

  // 导航回退处理 - 确保SPA路由正常工作
  workbox.routing.setDefaultHandler(new workbox.strategies.NetworkFirst({
    cacheName: "default-cache",
    networkTimeoutSeconds: 3,
    plugins: [
      new workbox.expiration.ExpirationPlugin({
        maxEntries: 50,
        maxAgeSeconds: 24 * 60 * 60 // 24小时
      })
    ]
  }));

  // 导航请求的回退处理
  workbox.routing.setCatchHandler(({ event }) => {
    // 如果是导航请求且失败，返回缓存的首页
    if (event.request.mode === 'navigate') {
      return caches.match('/') || caches.match('/home');
    }

    // 其他请求返回离线页面或错误
    return Response.error();
  });

  // 预缓存关键页面（安全版本）
  self.addEventListener('install', (event) => {
    event.waitUntil(
      caches.open('offline-pages-v1').then((cache) => {
        // 只缓存确定存在的页面
        const criticalPages = ['/'];
        return Promise.allSettled(
          criticalPages.map(url =>
            fetch(url).then(response => {
              if (response.ok) {
                return cache.put(url, response);
              }
            }).catch(err => {
              console.warn(`Failed to cache ${url}:`, err);
            })
          )
        );
      })
    );
  });

} else {
  console.log(`Boo! Workbox didn't load `);
}