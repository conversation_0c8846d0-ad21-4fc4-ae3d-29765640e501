/**
 * 全局性能优化插件
 */

import { performanceMonitor } from '@/utils/performanceMonitor'
import componentCache, { CacheMixin, memoryMonitor } from '@/utils/componentCache'
import { batchDOMUpdater, eventListenerManager } from '@/utils/domOptimizer'
import { initOfflineSupport } from '@/utils/offlineSupport'
import { cacheDebugger } from '@/utils/cacheDebugger'

/**
 * Vue性能优化插件
 */
export default {
  install(Vue, options = {}) {
    const defaultOptions = {
      enablePerformanceMonitoring: true,
      enableComponentCache: process.env.NODE_ENV !== 'production', // 生产环境默认禁用
      enableDOMOptimization: true,
      enableOfflineSupport: true,
      enableMemoryMonitoring: true,
      componentCacheOptions: {
        maxSize: 30, // 减少默认缓存大小
        defaultTTL: 180000, // 默认3分钟TTL
        enableInProduction: false // 生产环境安全开关
      },
      ...options
    }

    // 1. 性能监控
    if (defaultOptions.enablePerformanceMonitoring) {
      Vue.prototype.$performance = performanceMonitor
      
      // 添加全局性能指令
      Vue.directive('perf', {
        bind(el, binding) {
          const startTime = performance.now()
          el._perfStartTime = startTime
          
          if (binding.value && binding.value.name) {
            performance.mark(`${binding.value.name}-start`)
          }
        },
        inserted(el, binding) {
          const endTime = performance.now()
          const duration = endTime - el._perfStartTime
          
          if (binding.value && binding.value.name) {
            performance.mark(`${binding.value.name}-end`)
            performance.measure(binding.value.name, `${binding.value.name}-start`, `${binding.value.name}-end`)
          }
          
          if (duration > 16) { // 超过一帧的时间
            console.warn(`Performance warning: ${binding.value?.name || 'Element'} took ${duration.toFixed(2)}ms to render`)
          }
        }
      })
    }

    // 2. 组件缓存
    if (defaultOptions.enableComponentCache) {
      // 生产环境额外检查
      if (process.env.NODE_ENV === 'production' && !defaultOptions.componentCacheOptions.enableInProduction) {
        console.warn('[Performance] Component cache is disabled in production for data consistency. Set enableInProduction: true to enable.')
      } else {
        Vue.mixin(CacheMixin)
        Vue.prototype.$componentCache = componentCache

        // 添加缓存指令
        Vue.directive('cache', {
          bind(_el, binding, vnode) {
            if (binding.value && vnode.componentInstance) {
              vnode.componentInstance.$options.cache = binding.value
            }
          }
        })

        // 添加全局缓存控制方法
        Vue.prototype.$disableCache = function() {
          this.$options.cache = false
        }

        Vue.prototype.$enableCache = function(options = {}) {
          this.$options.cache = options
        }
      }
    }

    // 3. DOM优化
    if (defaultOptions.enableDOMOptimization) {
      Vue.prototype.$batchUpdate = batchDOMUpdater
      Vue.prototype.$eventManager = eventListenerManager
      
      // 优化的事件监听指令
      Vue.directive('on-optimized', {
        bind(el, binding) {
          const { event, handler, options = {} } = binding.value
          eventListenerManager.add(el, event, handler, {
            passive: true,
            throttle: 16, // 60fps
            ...options
          })
        },
        unbind(el, binding) {
          const { event, handler } = binding.value
          eventListenerManager.remove(el, event, handler)
        }
      })

      // 批量更新指令
      Vue.directive('batch-update', {
        update(el, binding) {
          batchDOMUpdater.schedule(() => {
            if (binding.value && typeof binding.value === 'function') {
              binding.value(el)
            }
          }, binding.arg || 3)
        }
      })
    }

    // 4. 内存监控
    if (defaultOptions.enableMemoryMonitoring) {
      Vue.prototype.$memoryMonitor = memoryMonitor
      
      // 全局混入内存监控
      Vue.mixin({
        beforeDestroy() {
          // 清理定时器
          if (this._timers) {
            this._timers.forEach(timer => clearTimeout(timer))
          }
          
          // 清理事件监听器
          if (this._listeners) {
            this._listeners.forEach(({ element, event, handler }) => {
              element.removeEventListener(event, handler)
            })
          }
          
          // 清理观察器
          if (this._observers) {
            this._observers.forEach(observer => observer.disconnect())
          }
        }
      })
    }

    // 5. 离线支持
    if (defaultOptions.enableOfflineSupport) {
      initOfflineSupport()

      // 添加缓存调试工具
      Vue.prototype.$cacheDebugger = cacheDebugger

      // 在开发模式下自动检查缓存状态
      if (process.env.NODE_ENV === 'development') {
        setTimeout(async () => {
          const report = await cacheDebugger.generateDiagnosticReport()
          if (report.recommendations.length > 0) {
            console.warn('Cache issues detected:', report.recommendations)
            console.log('Full diagnostic report:', report)
          }
        }, 5000)
      }
    }

    // 6. 全局性能优化方法
    Vue.prototype.$optimizeComponent = function(options = {}) {
      const component = this
      
      // 启用组件缓存
      if (options.cache !== false) {
        component.$options.cacheable = true
        component.$options.cacheKey = options.cacheKey || component.$options.name
      }
      
      // 优化计算属性
      if (options.optimizeComputed !== false) {
        const computed = component.$options.computed
        if (computed) {
          Object.keys(computed).forEach(key => {
            const original = computed[key]
            if (typeof original === 'function') {
              computed[key] = {
                get: original,
                cache: true
              }
            }
          })
        }
      }
      
      // 优化监听器
      if (options.optimizeWatchers !== false) {
        const watch = component.$options.watch
        if (watch) {
          Object.keys(watch).forEach(key => {
            const watcher = watch[key]
            if (typeof watcher === 'function') {
              watch[key] = {
                handler: watcher,
                deep: false,
                immediate: false
              }
            }
          })
        }
      }
    }

    // 7. 性能分析工具
    Vue.prototype.$analyzePerformance = function() {
      const report = performanceMonitor.getReport()
      const memoryInfo = memoryMonitor.getAverageUsage()
      const cacheStats = componentCache.getStats()
      
      return {
        performance: report,
        memory: {
          average: memoryInfo,
          current: memoryMonitor.measure(),
          isHigh: memoryMonitor.isMemoryHigh()
        },
        cache: cacheStats,
        recommendations: this.$getPerformanceRecommendations()
      }
    }

    Vue.prototype.$getPerformanceRecommendations = function() {
      const recommendations = []
      const report = performanceMonitor.getReport()
      
      if (report.longTasks > 5) {
        recommendations.push({
          type: 'performance',
          message: '检测到多个长任务，建议使用 v-batch-update 指令优化DOM操作'
        })
      }
      
      if (memoryMonitor.isMemoryHigh()) {
        recommendations.push({
          type: 'memory',
          message: '内存使用率过高，建议启用组件缓存和清理无用的事件监听器'
        })
      }
      
      if (report.resources.totalSize > 2 * 1024 * 1024) {
        recommendations.push({
          type: 'bundle',
          message: '资源包过大，建议启用代码分割和懒加载'
        })
      }
      
      return recommendations
    }

    // 8. 开发环境性能提示
    if (process.env.NODE_ENV === 'development') {
      Vue.config.performance = true
      
      // 组件渲染性能监控
      const originalMount = Vue.prototype.$mount
      Vue.prototype.$mount = function(...args) {
        const startTime = performance.now()
        const result = originalMount.apply(this, args)
        const endTime = performance.now()
        
        if (endTime - startTime > 16) {
          console.warn(`Component ${this.$options.name || 'Anonymous'} took ${(endTime - startTime).toFixed(2)}ms to mount`)
        }
        
        return result
      }
    }

    // 9. 全局错误处理优化
    Vue.config.errorHandler = function(err, vm, info) {
      console.error('Vue error:', err)
      console.error('Component:', vm)
      console.error('Info:', info)
      
      // 发送错误报告
      if (navigator.sendBeacon) {
        navigator.sendBeacon('/api/errors', JSON.stringify({
          error: err.message,
          stack: err.stack,
          component: vm?.$options.name,
          info,
          timestamp: Date.now(),
          userAgent: navigator.userAgent,
          url: location.href
        }))
      }
    }

    // 10. 路由性能优化
    if (options.router) {
      options.router.beforeEach((to, _from, next) => {
        // 预加载下一个可能访问的路由
        if (to.meta.preload) {
          to.meta.preload.forEach(routeName => {
            const route = options.router.resolve({ name: routeName })
            if (route.route.matched[0]?.components?.default) {
              route.route.matched[0].components.default()
            }
          })
        }
        
        next()
      })
    }

    console.log('Performance optimization plugin installed successfully')
  }
}
