<template>
  <div class="cache-debug-panel" v-if="showPanel">
    <div class="debug-header">
      <h3>缓存调试面板</h3>
      <button @click="closePanel" class="close-btn">×</button>
    </div>
    
    <div class="debug-content">
      <div class="status-section">
        <h4>缓存状态</h4>
        <div class="status-item" :class="{ error: !cacheStatus.cacheAPI }">
          <span>Cache API: </span>
          <span>{{ cacheStatus.cacheAPI ? '✓ 支持' : '✗ 不支持' }}</span>
        </div>
        <div class="status-item" :class="{ error: !cacheStatus.serviceWorker }">
          <span>Service Worker: </span>
          <span>{{ cacheStatus.serviceWorker ? '✓ 支持' : '✗ 不支持' }}</span>
        </div>
        <div class="status-item" :class="{ error: !cacheStatus.indexedDB }">
          <span>IndexedDB: </span>
          <span>{{ cacheStatus.indexedDB ? '✓ 支持' : '✗ 不支持' }}</span>
        </div>
      </div>

      <div class="caches-section" v-if="Object.keys(cacheStatus.caches).length > 0">
        <h4>当前缓存</h4>
        <div v-for="(cache, name) in cacheStatus.caches" :key="name" class="cache-item">
          <div class="cache-name">{{ name }}</div>
          <div class="cache-info">{{ cache.entries }} 个条目</div>
          <button @click="showCacheDetails(name, cache)" class="detail-btn">详情</button>
        </div>
      </div>

      <div class="errors-section" v-if="cacheStatus.errors.length > 0">
        <h4>错误信息</h4>
        <div v-for="(error, index) in cacheStatus.errors" :key="index" class="error-item">
          {{ error }}
        </div>
      </div>

      <div class="url-tests-section" v-if="urlTests.length > 0">
        <h4>URL 可访问性测试</h4>
        <div v-for="test in urlTests" :key="test.url" class="url-test-item" :class="{ error: !test.accessible }">
          <span class="url">{{ test.url }}</span>
          <span class="status">{{ test.accessible ? '✓' : '✗' }}</span>
          <span v-if="test.status" class="http-status">{{ test.status }}</span>
        </div>
      </div>

      <div class="actions-section">
        <button @click="refreshStatus" class="action-btn" :disabled="loading">
          {{ loading ? '检查中...' : '刷新状态' }}
        </button>
        <button @click="repairCache" class="action-btn repair-btn" :disabled="loading">
          {{ loading ? '修复中...' : '修复缓存' }}
        </button>
        <button @click="clearAllCaches" class="action-btn danger-btn" :disabled="loading">
          清空所有缓存
        </button>
      </div>

      <div class="recommendations-section" v-if="recommendations.length > 0">
        <h4>建议</h4>
        <ul>
          <li v-for="(rec, index) in recommendations" :key="index">{{ rec }}</li>
        </ul>
      </div>
    </div>

    <!-- 缓存详情模态框 -->
    <div v-if="showDetails" class="modal-overlay" @click="closeDetails">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h4>{{ selectedCacheName }} 详情</h4>
          <button @click="closeDetails" class="close-btn">×</button>
        </div>
        <div class="modal-body">
          <div v-for="url in selectedCacheUrls" :key="url" class="url-item">
            {{ url }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CacheDebugPanel',
  data() {
    return {
      showPanel: false,
      loading: false,
      cacheStatus: {
        cacheAPI: false,
        serviceWorker: false,
        indexedDB: false,
        caches: {},
        errors: []
      },
      urlTests: [],
      recommendations: [],
      showDetails: false,
      selectedCacheName: '',
      selectedCacheUrls: []
    }
  },
  mounted() {
    // 监听键盘快捷键 Ctrl+Shift+C 打开调试面板
    document.addEventListener('keydown', this.handleKeydown)
    
    // 如果URL包含debug参数，自动打开面板
    if (new URLSearchParams(window.location.search).has('cache-debug')) {
      this.openPanel()
    }
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.handleKeydown)
  },
  methods: {
    handleKeydown(event) {
      if (event.ctrlKey && event.shiftKey && event.key === 'C') {
        event.preventDefault()
        this.togglePanel()
      }
    },
    
    togglePanel() {
      if (this.showPanel) {
        this.closePanel()
      } else {
        this.openPanel()
      }
    },
    
    async openPanel() {
      this.showPanel = true
      await this.refreshStatus()
    },
    
    closePanel() {
      this.showPanel = false
      this.showDetails = false
    },
    
    async refreshStatus() {
      if (!this.$cacheDebugger) return
      
      this.loading = true
      try {
        const report = await this.$cacheDebugger.generateDiagnosticReport()
        this.cacheStatus = report.cacheStatus
        this.urlTests = report.urlTests
        this.recommendations = report.recommendations
      } catch (error) {
        console.error('Failed to refresh cache status:', error)
      } finally {
        this.loading = false
      }
    },
    
    async repairCache() {
      if (!this.$cacheDebugger) return
      
      this.loading = true
      try {
        const result = await this.$cacheDebugger.repairCache()
        if (result.success) {
          this.$message.success('缓存修复成功')
          await this.refreshStatus()
        } else {
          this.$message.error('缓存修复失败: ' + result.errors.join(', '))
        }
      } catch (error) {
        this.$message.error('缓存修复失败: ' + error.message)
      } finally {
        this.loading = false
      }
    },
    
    async clearAllCaches() {
      if (!this.$cacheDebugger) return
      
      if (!confirm('确定要清空所有缓存吗？这将删除所有离线数据。')) {
        return
      }
      
      this.loading = true
      try {
        const result = await this.$cacheDebugger.clearAllCaches()
        if (result.success) {
          this.$message.success('所有缓存已清空')
          await this.refreshStatus()
        } else {
          this.$message.error('清空缓存失败: ' + result.error)
        }
      } catch (error) {
        this.$message.error('清空缓存失败: ' + error.message)
      } finally {
        this.loading = false
      }
    },
    
    showCacheDetails(name, cache) {
      this.selectedCacheName = name
      this.selectedCacheUrls = cache.urls
      this.showDetails = true
    },
    
    closeDetails() {
      this.showDetails = false
    }
  }
}
</script>

<style scoped>
.cache-debug-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 400px;
  max-height: 80vh;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  z-index: 9999;
  overflow: hidden;
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.debug-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.debug-content {
  padding: 16px;
  max-height: calc(80vh - 60px);
  overflow-y: auto;
}

.status-section, .caches-section, .errors-section, .url-tests-section, .actions-section, .recommendations-section {
  margin-bottom: 16px;
}

.status-section h4, .caches-section h4, .errors-section h4, .url-tests-section h4, .recommendations-section h4 {
  margin: 0 0 8px 0;
  font-size: 12px;
  font-weight: 600;
  color: #666;
}

.status-item, .cache-item, .url-test-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  font-size: 12px;
}

.status-item.error, .url-test-item.error {
  color: #ff4d4f;
}

.cache-item {
  border-bottom: 1px solid #f0f0f0;
  padding: 8px 0;
}

.cache-name {
  font-weight: 500;
  font-size: 11px;
}

.cache-info {
  color: #666;
  font-size: 10px;
}

.detail-btn, .action-btn {
  background: #1890ff;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 10px;
}

.action-btn {
  margin-right: 8px;
  margin-bottom: 8px;
  padding: 6px 12px;
}

.repair-btn {
  background: #52c41a;
}

.danger-btn {
  background: #ff4d4f;
}

.action-btn:disabled {
  background: #d9d9d9;
  cursor: not-allowed;
}

.error-item {
  background: #fff2f0;
  border: 1px solid #ffccc7;
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 4px;
  font-size: 11px;
  color: #a8071a;
}

.url {
  flex: 1;
  font-size: 11px;
}

.status {
  margin-left: 8px;
}

.http-status {
  margin-left: 4px;
  font-size: 10px;
  color: #666;
}

.recommendations-section ul {
  margin: 0;
  padding-left: 16px;
}

.recommendations-section li {
  font-size: 11px;
  margin-bottom: 4px;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  background: white;
  border-radius: 8px;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #ddd;
}

.modal-body {
  padding: 16px;
  max-height: 60vh;
  overflow-y: auto;
}

.url-item {
  padding: 4px 0;
  font-size: 12px;
  word-break: break-all;
  border-bottom: 1px solid #f0f0f0;
}
</style>
