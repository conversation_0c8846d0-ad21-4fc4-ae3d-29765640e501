/**
 * 离线支持和PWA增强功能
 */

/**
 * 离线数据管理器
 */
export class OfflineDataManager {
  constructor() {
    this.dbName = 'BlackbearOfflineDB'
    this.version = 1
    this.db = null
    this.init()
  }

  async init() {
    if ('indexedDB' in window) {
      try {
        this.db = await this.openDB()
      } catch (error) {
        console.error('Failed to initialize offline database:', error)
      }
    }
  }

  openDB() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version)
      
      request.onerror = () => reject(request.error)
      request.onsuccess = () => resolve(request.result)
      
      request.onupgradeneeded = (event) => {
        const db = event.target.result
        
        // 创建新闻存储
        if (!db.objectStoreNames.contains('news')) {
          const newsStore = db.createObjectStore('news', { keyPath: 'id' })
          newsStore.createIndex('timestamp', 'timestamp', { unique: false })
          newsStore.createIndex('category', 'category', { unique: false })
        }
        
        // 创建股票数据存储
        if (!db.objectStoreNames.contains('stocks')) {
          const stocksStore = db.createObjectStore('stocks', { keyPath: 'code' })
          stocksStore.createIndex('timestamp', 'timestamp', { unique: false })
        }
        
        // 创建用户数据存储
        if (!db.objectStoreNames.contains('userData')) {
          db.createObjectStore('userData', { keyPath: 'key' })
        }
      }
    })
  }

  async saveData(storeName, data) {
    if (!this.db) return false
    
    try {
      const transaction = this.db.transaction([storeName], 'readwrite')
      const store = transaction.objectStore(storeName)
      
      if (Array.isArray(data)) {
        for (const item of data) {
          await store.put({ ...item, timestamp: Date.now() })
        }
      } else {
        await store.put({ ...data, timestamp: Date.now() })
      }
      
      return true
    } catch (error) {
      console.error('Failed to save offline data:', error)
      return false
    }
  }

  async getData(storeName, key = null) {
    if (!this.db) return null
    
    try {
      const transaction = this.db.transaction([storeName], 'readonly')
      const store = transaction.objectStore(storeName)
      
      if (key) {
        const request = store.get(key)
        return new Promise((resolve) => {
          request.onsuccess = () => resolve(request.result)
          request.onerror = () => resolve(null)
        })
      } else {
        const request = store.getAll()
        return new Promise((resolve) => {
          request.onsuccess = () => resolve(request.result)
          request.onerror = () => resolve([])
        })
      }
    } catch (error) {
      console.error('Failed to get offline data:', error)
      return null
    }
  }

  async clearExpiredData(storeName, maxAge = 7 * 24 * 60 * 60 * 1000) {
    if (!this.db) return
    
    try {
      const transaction = this.db.transaction([storeName], 'readwrite')
      const store = transaction.objectStore(storeName)
      const index = store.index('timestamp')
      
      const cutoffTime = Date.now() - maxAge
      const range = IDBKeyRange.upperBound(cutoffTime)
      
      const request = index.openCursor(range)
      request.onsuccess = (event) => {
        const cursor = event.target.result
        if (cursor) {
          cursor.delete()
          cursor.continue()
        }
      }
    } catch (error) {
      console.error('Failed to clear expired data:', error)
    }
  }
}

/**
 * 网络状态管理器
 */
export class NetworkStatusManager {
  constructor() {
    this.isOnline = navigator.onLine
    this.callbacks = {
      online: [],
      offline: []
    }
    
    this.init()
  }

  init() {
    window.addEventListener('online', this.handleOnline.bind(this))
    window.addEventListener('offline', this.handleOffline.bind(this))
  }

  handleOnline() {
    this.isOnline = true
    this.callbacks.online.forEach(callback => callback())
  }

  handleOffline() {
    this.isOnline = false
    this.callbacks.offline.forEach(callback => callback())
  }

  onOnline(callback) {
    this.callbacks.online.push(callback)
  }

  onOffline(callback) {
    this.callbacks.offline.push(callback)
  }

  removeListener(event, callback) {
    const index = this.callbacks[event].indexOf(callback)
    if (index > -1) {
      this.callbacks[event].splice(index, 1)
    }
  }
}

/**
 * 后台同步管理器
 */
export class BackgroundSyncManager {
  constructor() {
    this.syncQueue = []
    this.isProcessing = false
  }

  /**
   * 添加同步任务
   * @param {string} type 任务类型
   * @param {Object} data 数据
   * @param {Function} syncFn 同步函数
   */
  addTask(type, data, syncFn) {
    this.syncQueue.push({
      id: Date.now() + Math.random(),
      type,
      data,
      syncFn,
      timestamp: Date.now(),
      retries: 0,
      maxRetries: 3
    })
    
    this.processSyncQueue()
  }

  async processSyncQueue() {
    if (this.isProcessing || this.syncQueue.length === 0) return
    
    this.isProcessing = true
    
    while (this.syncQueue.length > 0) {
      const task = this.syncQueue.shift()
      
      try {
        await task.syncFn(task.data)
        console.log(`Background sync completed for task: ${task.type}`)
      } catch (error) {
        console.error(`Background sync failed for task: ${task.type}`, error)
        
        task.retries++
        if (task.retries < task.maxRetries) {
          // 重新加入队列，延迟重试
          setTimeout(() => {
            this.syncQueue.push(task)
          }, Math.pow(2, task.retries) * 1000) // 指数退避
        }
      }
    }
    
    this.isProcessing = false
  }

  clearQueue() {
    this.syncQueue = []
  }
}

/**
 * PWA更新管理器
 */
export class PWAUpdateManager {
  constructor() {
    this.registration = null
    this.updateAvailable = false
    this.callbacks = {
      updateAvailable: [],
      updateInstalled: []
    }
    
    this.init()
  }

  async init() {
    if ('serviceWorker' in navigator) {
      try {
        this.registration = await navigator.serviceWorker.register('/service-worker.js')
        this.setupUpdateListener()
      } catch (error) {
        console.error('Service Worker registration failed:', error)
      }
    }
  }

  setupUpdateListener() {
    if (!this.registration) return
    
    this.registration.addEventListener('updatefound', () => {
      const newWorker = this.registration.installing
      
      newWorker.addEventListener('statechange', () => {
        if (newWorker.state === 'installed') {
          if (navigator.serviceWorker.controller) {
            // 有新版本可用
            this.updateAvailable = true
            this.callbacks.updateAvailable.forEach(callback => callback())
          } else {
            // 首次安装
            this.callbacks.updateInstalled.forEach(callback => callback())
          }
        }
      })
    })
  }

  onUpdateAvailable(callback) {
    this.callbacks.updateAvailable.push(callback)
  }

  onUpdateInstalled(callback) {
    this.callbacks.updateInstalled.push(callback)
  }

  async skipWaiting() {
    if (this.registration && this.registration.waiting) {
      this.registration.waiting.postMessage({ type: 'SKIP_WAITING' })
    }
  }

  async checkForUpdates() {
    if (this.registration) {
      await this.registration.update()
    }
  }
}

/**
 * 离线页面缓存
 */
export class OfflinePageCache {
  constructor() {
    this.cacheName = 'offline-pages-v1'
    this.pagesToCache = [
      '/',
      '/home',
      '/app/dashboard'
    ]
  }

  async cachePages() {
    if ('caches' in window) {
      try {
        const cache = await caches.open(this.cacheName)
        // 使用更宽容的缓存策略，单独处理每个URL
        await this.cachePagesSafely(cache, this.pagesToCache)
      } catch (error) {
        console.error('Failed to cache pages:', error)
      }
    }
  }

  async cachePagesSafely(cache, urls) {
    const results = await Promise.allSettled(
      urls.map(async (url) => {
        try {
          // 先检查URL是否可访问
          const response = await fetch(url, {
            method: 'HEAD',
            cache: 'no-cache'
          })

          if (response.ok) {
            await cache.add(url)
            console.log(`Successfully cached: ${url}`)
            return { url, status: 'success' }
          } else {
            console.warn(`Skipping cache for ${url}: ${response.status}`)
            return { url, status: 'skipped', reason: `HTTP ${response.status}` }
          }
        } catch (error) {
          console.warn(`Failed to cache ${url}:`, error.message)
          return { url, status: 'failed', error: error.message }
        }
      })
    )

    // 记录缓存结果
    const successful = results.filter(r => r.value?.status === 'success').length
    const failed = results.filter(r => r.value?.status === 'failed').length
    const skipped = results.filter(r => r.value?.status === 'skipped').length

    console.log(`Cache summary: ${successful} successful, ${failed} failed, ${skipped} skipped`)

    return results
  }

  async getOfflinePage(url) {
    if ('caches' in window) {
      try {
        const cache = await caches.open(this.cacheName)
        const response = await cache.match(url)
        return response
      } catch (error) {
        console.error('Failed to get offline page:', error)
        return null
      }
    }
    return null
  }

  async validateAndUpdateCache() {
    if ('caches' in window) {
      try {
        const cache = await caches.open(this.cacheName)

        // 检查当前缓存的页面
        const cachedRequests = await cache.keys()
        console.log('Currently cached pages:', cachedRequests.map(req => req.url))

        // 验证每个缓存的页面是否仍然有效
        for (const request of cachedRequests) {
          try {
            const response = await fetch(request.url, {
              method: 'HEAD',
              cache: 'no-cache'
            })

            if (!response.ok) {
              console.warn(`Removing invalid cached page: ${request.url}`)
              await cache.delete(request)
            }
          } catch (error) {
            console.warn(`Error validating cached page ${request.url}:`, error)
          }
        }

        // 尝试缓存新的页面
        await this.cachePages()

      } catch (error) {
        console.error('Failed to validate and update cache:', error)
      }
    }
  }

  async clearCache() {
    if ('caches' in window) {
      try {
        await caches.delete(this.cacheName)
        console.log('Offline page cache cleared')
      } catch (error) {
        console.error('Failed to clear cache:', error)
      }
    }
  }
}

// 创建全局实例
export const offlineDataManager = new OfflineDataManager()
export const networkStatusManager = new NetworkStatusManager()
export const backgroundSyncManager = new BackgroundSyncManager()
export const pwaUpdateManager = new PWAUpdateManager()
export const offlinePageCache = new OfflinePageCache()

// 初始化离线支持
export function initOfflineSupport() {
  // 延迟初始化缓存，避免阻塞应用启动
  setTimeout(async () => {
    try {
      // 先验证和更新缓存
      await offlinePageCache.validateAndUpdateCache()
    } catch (error) {
      console.error('Failed to initialize offline page cache:', error)
    }
  }, 2000) // 延迟2秒执行

  // 监听网络状态变化
  networkStatusManager.onOnline(() => {
    console.log('Network is back online')
    // 网络恢复时，验证和更新缓存
    offlinePageCache.validateAndUpdateCache()
    // 处理离线期间的同步任务
    backgroundSyncManager.processSyncQueue()
  })

  networkStatusManager.onOffline(() => {
    console.log('Network is offline')
    // 显示离线提示
    showOfflineNotification()
  })

  // 监听PWA更新
  pwaUpdateManager.onUpdateAvailable(() => {
    showUpdateNotification()
  })

  // 定期清理过期的离线数据和验证缓存
  setInterval(() => {
    offlineDataManager.clearExpiredData('news')
    offlineDataManager.clearExpiredData('stocks')
    // 每天验证一次缓存
    offlinePageCache.validateAndUpdateCache()
  }, 24 * 60 * 60 * 1000) // 每天清理一次
}

function showOfflineNotification() {
  // 这里可以集成到Vue的通知系统
  console.log('App is now offline. Some features may be limited.')
}

function showUpdateNotification() {
  // 这里可以集成到Vue的通知系统
  console.log('A new version is available. Please refresh to update.')
}

export default {
  OfflineDataManager,
  NetworkStatusManager,
  BackgroundSyncManager,
  PWAUpdateManager,
  OfflinePageCache,
  initOfflineSupport
}
