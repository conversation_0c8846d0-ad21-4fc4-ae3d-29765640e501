/**
 * 缓存调试工具
 * 用于诊断和修复缓存相关问题
 */

export class CacheDebugger {
  constructor() {
    this.isDebugMode = process.env.NODE_ENV === 'development' || localStorage.getItem('cache-debug') === 'true'
  }

  /**
   * 启用调试模式
   */
  enableDebug() {
    localStorage.setItem('cache-debug', 'true')
    this.isDebugMode = true
    console.log('Cache debugging enabled')
  }

  /**
   * 禁用调试模式
   */
  disableDebug() {
    localStorage.removeItem('cache-debug')
    this.isDebugMode = false
    console.log('Cache debugging disabled')
  }

  /**
   * 调试日志
   */
  log(message, ...args) {
    if (this.isDebugMode) {
      console.log(`[CacheDebug] ${message}`, ...args)
    }
  }

  /**
   * 警告日志
   */
  warn(message, ...args) {
    if (this.isDebugMode) {
      console.warn(`[CacheDebug] ${message}`, ...args)
    }
  }

  /**
   * 错误日志
   */
  error(message, ...args) {
    console.error(`[CacheDebug] ${message}`, ...args)
  }

  /**
   * 检查缓存状态
   */
  async checkCacheStatus() {
    const report = {
      cacheAPI: 'caches' in window,
      serviceWorker: 'serviceWorker' in navigator,
      indexedDB: 'indexedDB' in window,
      caches: {},
      errors: []
    }

    if (report.cacheAPI) {
      try {
        const cacheNames = await caches.keys()
        this.log('Available caches:', cacheNames)
        
        for (const cacheName of cacheNames) {
          try {
            const cache = await caches.open(cacheName)
            const keys = await cache.keys()
            report.caches[cacheName] = {
              entries: keys.length,
              urls: keys.map(req => req.url)
            }
          } catch (error) {
            report.errors.push(`Failed to inspect cache ${cacheName}: ${error.message}`)
          }
        }
      } catch (error) {
        report.errors.push(`Failed to get cache names: ${error.message}`)
      }
    }

    return report
  }

  /**
   * 测试URL是否可访问
   */
  async testURL(url) {
    try {
      const response = await fetch(url, { 
        method: 'HEAD',
        cache: 'no-cache'
      })
      
      return {
        url,
        accessible: response.ok,
        status: response.status,
        statusText: response.statusText
      }
    } catch (error) {
      return {
        url,
        accessible: false,
        error: error.message
      }
    }
  }

  /**
   * 批量测试URL
   */
  async testURLs(urls) {
    const results = await Promise.allSettled(
      urls.map(url => this.testURL(url))
    )
    
    return results.map(result => 
      result.status === 'fulfilled' ? result.value : { error: result.reason }
    )
  }

  /**
   * 清理所有缓存
   */
  async clearAllCaches() {
    try {
      const cacheNames = await caches.keys()
      const deletePromises = cacheNames.map(name => caches.delete(name))
      await Promise.all(deletePromises)
      
      this.log(`Cleared ${cacheNames.length} caches:`, cacheNames)
      return { success: true, cleared: cacheNames }
    } catch (error) {
      this.error('Failed to clear caches:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 修复缓存问题
   */
  async repairCache() {
    this.log('Starting cache repair...')
    
    const report = {
      steps: [],
      success: false,
      errors: []
    }

    try {
      // 1. 检查当前状态
      report.steps.push('Checking current cache status')
      const status = await this.checkCacheStatus()
      
      // 2. 清理损坏的缓存
      report.steps.push('Clearing potentially corrupted caches')
      const clearResult = await this.clearAllCaches()
      
      if (!clearResult.success) {
        report.errors.push(`Failed to clear caches: ${clearResult.error}`)
      }

      // 3. 重新注册Service Worker
      if ('serviceWorker' in navigator) {
        report.steps.push('Re-registering service worker')
        try {
          const registration = await navigator.serviceWorker.getRegistration()
          if (registration) {
            await registration.unregister()
            this.log('Service worker unregistered')
          }
          
          // 重新注册
          await navigator.serviceWorker.register('/service-worker.js')
          this.log('Service worker re-registered')
        } catch (error) {
          report.errors.push(`Service worker error: ${error.message}`)
        }
      }

      // 4. 重新初始化缓存
      report.steps.push('Re-initializing caches')
      
      report.success = report.errors.length === 0
      this.log('Cache repair completed:', report)
      
    } catch (error) {
      report.errors.push(`Repair failed: ${error.message}`)
      this.error('Cache repair failed:', error)
    }

    return report
  }

  /**
   * 生成诊断报告
   */
  async generateDiagnosticReport() {
    const report = {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      cacheStatus: await this.checkCacheStatus(),
      urlTests: await this.testURLs([
        '/',
        '/home',
        '/app/dashboard'
      ]),
      recommendations: []
    }

    // 生成建议
    if (!report.cacheStatus.cacheAPI) {
      report.recommendations.push('Cache API not supported in this browser')
    }

    if (!report.cacheStatus.serviceWorker) {
      report.recommendations.push('Service Worker not supported in this browser')
    }

    if (report.cacheStatus.errors.length > 0) {
      report.recommendations.push('Cache errors detected - consider running cache repair')
    }

    const failedUrls = report.urlTests.filter(test => !test.accessible)
    if (failedUrls.length > 0) {
      report.recommendations.push(`${failedUrls.length} URLs are not accessible and should be removed from cache list`)
    }

    return report
  }
}

// 创建全局实例
export const cacheDebugger = new CacheDebugger()

// 在开发模式下暴露到全局
if (process.env.NODE_ENV === 'development') {
  window.cacheDebugger = cacheDebugger
}
