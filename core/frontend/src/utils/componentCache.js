/**
 * 高性能组件缓存管理器
 */

class ComponentCache {
  constructor(maxSize = 50) {
    this.cache = new Map()
    this.maxSize = maxSize
    this.accessOrder = []
    this.hitCount = 0
    this.missCount = 0
    this.setCount = 0
    this.evictCount = 0
  }

  /**
   * 获取缓存的组件
   * @param {string} key 缓存键
   */
  get(key) {
    if (this.cache.has(key)) {
      // 更新访问顺序
      this.updateAccessOrder(key)
      this.hitCount++
      return this.cache.get(key)
    }
    this.missCount++
    return null
  }

  /**
   * 设置组件缓存
   * @param {string} key 缓存键
   * @param {Object} component 组件实例
   */
  set(key, component) {
    if (this.cache.has(key)) {
      this.updateAccessOrder(key)
    } else {
      if (this.cache.size >= this.maxSize) {
        this.evictLRU()
      }
      this.accessOrder.push(key)
    }

    this.cache.set(key, component)
    this.setCount++
  }

  /**
   * 删除缓存
   * @param {string} key 缓存键
   */
  delete(key) {
    if (this.cache.has(key)) {
      this.cache.delete(key)
      const index = this.accessOrder.indexOf(key)
      if (index > -1) {
        this.accessOrder.splice(index, 1)
      }
    }
  }

  /**
   * 清空缓存
   */
  clear() {
    this.cache.clear()
    this.accessOrder = []
  }

  /**
   * 更新访问顺序
   * @param {string} key 缓存键
   */
  updateAccessOrder(key) {
    const index = this.accessOrder.indexOf(key)
    if (index > -1) {
      this.accessOrder.splice(index, 1)
    }
    this.accessOrder.push(key)
  }

  /**
   * LRU淘汰策略
   */
  evictLRU() {
    const lruKey = this.accessOrder.shift()
    if (lruKey) {
      this.cache.delete(lruKey)
      this.evictCount++
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: this.hitCount / (this.hitCount + this.missCount) || 0,
      hitCount: this.hitCount,
      missCount: this.missCount,
      setCount: this.setCount,
      evictCount: this.evictCount,
      keys: Array.from(this.cache.keys())
    }
  }

  /**
   * 重置统计信息
   */
  resetStats() {
    this.hitCount = 0
    this.missCount = 0
    this.setCount = 0
    this.evictCount = 0
  }

  /**
   * 清理过期缓存
   */
  cleanupExpired() {
    const now = Date.now()
    const keysToDelete = []

    for (const [key, value] of this.cache.entries()) {
      if (value.timestamp && value.ttl && (now - value.timestamp > value.ttl)) {
        keysToDelete.push(key)
      }
    }

    keysToDelete.forEach(key => this.delete(key))
    return keysToDelete.length
  }
}

// 创建全局组件缓存实例
const componentCache = new ComponentCache()

/**
 * 内存监控器
 */
export const memoryMonitor = {
  /**
   * 获取内存使用情况
   */
  getMemoryUsage() {
    if (performance.memory) {
      return {
        used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
        limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
      }
    }
    return null
  },

  /**
   * 检查内存压力
   */
  checkMemoryPressure() {
    const memory = this.getMemoryUsage()
    if (!memory) return false

    const usageRatio = memory.used / memory.limit
    return usageRatio > 0.8 // 超过80%认为有内存压力
  },

  /**
   * 自动清理缓存
   */
  autoCleanup() {
    if (this.checkMemoryPressure()) {
      const cleaned = componentCache.cleanupExpired()
      if (cleaned === 0 && componentCache.cache.size > 10) {
        // 如果没有过期缓存可清理，清理一半最旧的缓存
        const keysToDelete = componentCache.accessOrder.slice(0, Math.floor(componentCache.cache.size / 2))
        keysToDelete.forEach(key => componentCache.delete(key))

        if (process.env.NODE_ENV === 'development') {
          console.warn(`[MemoryMonitor] Memory pressure detected, cleaned ${keysToDelete.length} cache entries`)
        }
      }
    }
  },

  /**
   * 开始监控
   */
  startMonitoring(interval = 30000) {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
    }

    this.monitoringInterval = setInterval(() => {
      this.autoCleanup()
    }, interval)
  },

  /**
   * 停止监控
   */
  stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = null
    }
  }
}

/**
 * 智能Vue组件缓存混入
 */
export const CacheMixin = {
  beforeCreate() {
    const cacheOptions = this.$options.cache
    if (!cacheOptions || cacheOptions === false) return

    const cacheKey = this.getCacheKey()
    if (cacheKey) {
      const cached = componentCache.get(cacheKey)
      if (cached && this.isCacheValid(cached)) {
        // 只恢复可缓存的数据
        const cacheableData = this.getCacheableData(cached.data)
        Object.assign(this.$data, cacheableData)

        // 标记为从缓存恢复
        this._fromCache = true
        this._cacheTimestamp = cached.timestamp

        if (process.env.NODE_ENV === 'development') {
          console.log(`[ComponentCache] Restored from cache: ${cacheKey}`)
        }
      }
    }
  },

  beforeDestroy() {
    const cacheOptions = this.$options.cache
    if (!cacheOptions || cacheOptions === false) return

    const cacheKey = this.getCacheKey()
    if (cacheKey) {
      // 只缓存指定的数据
      const dataToCache = this.getDataToCache()
      if (dataToCache && Object.keys(dataToCache).length > 0) {
        componentCache.set(cacheKey, {
          data: dataToCache,
          timestamp: Date.now(),
          version: this.getDataVersion?.() || 1
        })

        if (process.env.NODE_ENV === 'development') {
          console.log(`[ComponentCache] Cached data: ${cacheKey}`, dataToCache)
        }
      }
    }
  },

  methods: {
    /**
     * 获取缓存键
     */
    getCacheKey() {
      const cacheOptions = this.$options.cache
      if (typeof cacheOptions === 'string') {
        return cacheOptions
      }
      if (typeof cacheOptions === 'object' && cacheOptions.key) {
        return typeof cacheOptions.key === 'function'
          ? cacheOptions.key.call(this)
          : cacheOptions.key
      }
      return this.$options.name || this.$options._componentTag
    },

    /**
     * 检查缓存是否有效
     */
    isCacheValid(cached) {
      const cacheOptions = this.$options.cache
      const ttl = (typeof cacheOptions === 'object' && cacheOptions.ttl) || 300000 // 默认5分钟

      // 检查时间过期
      if (Date.now() - cached.timestamp > ttl) {
        return false
      }

      // 检查版本
      const currentVersion = this.getDataVersion?.() || 1
      if (cached.version && cached.version !== currentVersion) {
        return false
      }

      // 自定义验证
      if (typeof cacheOptions === 'object' && cacheOptions.validate) {
        return cacheOptions.validate.call(this, cached)
      }

      return true
    },

    /**
     * 获取可缓存的数据
     */
    getCacheableData(cachedData) {
      const cacheOptions = this.$options.cache

      if (typeof cacheOptions === 'object' && cacheOptions.include) {
        const include = cacheOptions.include
        const result = {}
        include.forEach(key => {
          if (cachedData.hasOwnProperty(key)) {
            result[key] = cachedData[key]
          }
        })
        return result
      }

      if (typeof cacheOptions === 'object' && cacheOptions.exclude) {
        const exclude = cacheOptions.exclude
        const result = { ...cachedData }
        exclude.forEach(key => {
          delete result[key]
        })
        return result
      }

      return cachedData
    },

    /**
     * 获取要缓存的数据
     */
    getDataToCache() {
      const cacheOptions = this.$options.cache

      if (typeof cacheOptions === 'object' && cacheOptions.include) {
        const include = cacheOptions.include
        const result = {}
        include.forEach(key => {
          if (this.$data.hasOwnProperty(key)) {
            result[key] = this.$data[key]
          }
        })
        return result
      }

      if (typeof cacheOptions === 'object' && cacheOptions.exclude) {
        const exclude = cacheOptions.exclude
        const result = { ...this.$data }
        exclude.forEach(key => {
          delete result[key]
        })
        return result
      }

      return { ...this.$data }
    },

    /**
     * 手动清除组件缓存
     */
    clearCache() {
      const cacheKey = this.getCacheKey()
      if (cacheKey) {
        componentCache.delete(cacheKey)
        if (process.env.NODE_ENV === 'development') {
          console.log(`[ComponentCache] Cleared cache: ${cacheKey}`)
        }
      }
    },

    /**
     * 手动刷新缓存
     */
    refreshCache() {
      this.clearCache()
      const cacheKey = this.getCacheKey()
      if (cacheKey) {
        const dataToCache = this.getDataToCache()
        componentCache.set(cacheKey, {
          data: dataToCache,
          timestamp: Date.now(),
          version: this.getDataVersion?.() || 1
        })
      }
    }
  }
}

/**
 * 计算属性缓存装饰器
 */
export function computedCache(target, propertyKey, descriptor) {
  const originalMethod = descriptor.get
  const cacheKey = `computed_${target.constructor.name}_${propertyKey}`
  
  descriptor.get = function() {
    const cached = componentCache.get(cacheKey)
    if (cached && cached.timestamp > Date.now() - 5000) { // 5秒缓存
      return cached.value
    }
    
    const result = originalMethod.call(this)
    componentCache.set(cacheKey, {
      value: result,
      timestamp: Date.now()
    })
    
    return result
  }
  
  return descriptor
}

/**
 * 方法结果缓存装饰器
 */
export function methodCache(ttl = 5000) {
  return function(_target, _propertyKey, descriptor) {
    const originalMethod = descriptor.value
    const cache = new Map()
    
    descriptor.value = function(...args) {
      const cacheKey = JSON.stringify(args)
      const cached = cache.get(cacheKey)
      
      if (cached && cached.timestamp > Date.now() - ttl) {
        return cached.value
      }
      
      const result = originalMethod.apply(this, args)
      cache.set(cacheKey, {
        value: result,
        timestamp: Date.now()
      })
      
      // 清理过期缓存
      setTimeout(() => {
        cache.delete(cacheKey)
      }, ttl)
      
      return result
    }
    
    return descriptor
  }
}

/**
 * 内存使用监控
 */
export class MemoryMonitor {
  constructor() {
    this.measurements = []
    this.maxMeasurements = 100
  }

  measure() {
    if (performance.memory) {
      const memory = {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit,
        timestamp: Date.now()
      }
      
      this.measurements.push(memory)
      
      if (this.measurements.length > this.maxMeasurements) {
        this.measurements.shift()
      }
      
      return memory
    }
    return null
  }

  getAverageUsage() {
    if (this.measurements.length === 0) return 0
    
    const total = this.measurements.reduce((sum, m) => sum + m.used, 0)
    return total / this.measurements.length
  }

  isMemoryHigh() {
    const latest = this.measurements[this.measurements.length - 1]
    if (!latest) return false
    
    return latest.used / latest.limit > 0.8 // 80%阈值
  }

  cleanup() {
    if (this.isMemoryHigh()) {
      // 触发垃圾回收建议
      if (window.gc) {
        window.gc()
      }
      
      // 清理组件缓存
      componentCache.clear()
      
      console.warn('Memory usage high, performed cleanup')
    }
  }
}

// 启动内存监控
memoryMonitor.startMonitoring()

export default componentCache
