/**
 * 缓存修复脚本
 * 可以在浏览器控制台中直接运行
 */

window.fixCache = async function() {
  console.log('🔧 开始修复缓存问题...')
  
  const results = {
    steps: [],
    errors: [],
    success: false
  }
  
  try {
    // 1. 检查浏览器支持
    console.log('1️⃣ 检查浏览器支持...')
    results.steps.push('检查浏览器支持')
    
    if (!('caches' in window)) {
      throw new Error('浏览器不支持 Cache API')
    }
    
    if (!('serviceWorker' in navigator)) {
      console.warn('⚠️ 浏览器不支持 Service Worker')
    }
    
    // 2. 清理现有缓存
    console.log('2️⃣ 清理现有缓存...')
    results.steps.push('清理现有缓存')
    
    const cacheNames = await caches.keys()
    console.log('发现缓存:', cacheNames)
    
    for (const cacheName of cacheNames) {
      await caches.delete(cacheName)
      console.log(`✅ 已删除缓存: ${cacheName}`)
    }
    
    // 3. 重新注册 Service Worker
    if ('serviceWorker' in navigator) {
      console.log('3️⃣ 重新注册 Service Worker...')
      results.steps.push('重新注册 Service Worker')
      
      const registrations = await navigator.serviceWorker.getRegistrations()
      for (const registration of registrations) {
        await registration.unregister()
        console.log('✅ 已注销 Service Worker')
      }
      
      try {
        await navigator.serviceWorker.register('/service-worker.js')
        console.log('✅ Service Worker 重新注册成功')
      } catch (error) {
        console.warn('⚠️ Service Worker 注册失败:', error.message)
        results.errors.push(`Service Worker 注册失败: ${error.message}`)
      }
    }
    
    // 4. 测试关键页面
    console.log('4️⃣ 测试关键页面可访问性...')
    results.steps.push('测试关键页面可访问性')
    
    const testUrls = ['/', '/home']
    const urlResults = []
    
    for (const url of testUrls) {
      try {
        const response = await fetch(url, { method: 'HEAD', cache: 'no-cache' })
        const accessible = response.ok
        urlResults.push({ url, accessible, status: response.status })
        console.log(`${accessible ? '✅' : '❌'} ${url} - ${response.status}`)
      } catch (error) {
        urlResults.push({ url, accessible: false, error: error.message })
        console.log(`❌ ${url} - ${error.message}`)
      }
    }
    
    // 5. 重新创建基础缓存
    console.log('5️⃣ 重新创建基础缓存...')
    results.steps.push('重新创建基础缓存')
    
    const cache = await caches.open('offline-pages-v1')
    const validUrls = urlResults.filter(r => r.accessible).map(r => r.url)
    
    if (validUrls.length > 0) {
      try {
        await cache.addAll(validUrls)
        console.log('✅ 基础页面缓存成功:', validUrls)
      } catch (error) {
        console.warn('⚠️ 批量缓存失败，尝试单独缓存...')
        for (const url of validUrls) {
          try {
            await cache.add(url)
            console.log(`✅ 单独缓存成功: ${url}`)
          } catch (err) {
            console.warn(`❌ 缓存失败: ${url} - ${err.message}`)
            results.errors.push(`缓存失败: ${url} - ${err.message}`)
          }
        }
      }
    }
    
    // 6. 清理 localStorage 中的过期数据
    console.log('6️⃣ 清理本地存储...')
    results.steps.push('清理本地存储')
    
    const keysToRemove = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && (key.includes('cache') || key.includes('offline'))) {
        keysToRemove.push(key)
      }
    }
    
    keysToRemove.forEach(key => {
      localStorage.removeItem(key)
      console.log(`✅ 已清理本地存储: ${key}`)
    })
    
    results.success = true
    console.log('🎉 缓存修复完成!')
    
    // 显示修复结果
    console.log('\n📊 修复结果:')
    console.log('执行步骤:', results.steps)
    if (results.errors.length > 0) {
      console.log('错误信息:', results.errors)
    }
    console.log('URL 测试结果:', urlResults)
    
    // 建议用户刷新页面
    console.log('\n💡 建议刷新页面以应用修复结果')
    
    return {
      success: results.success,
      steps: results.steps,
      errors: results.errors,
      urlResults
    }
    
  } catch (error) {
    console.error('❌ 修复过程中发生错误:', error)
    results.errors.push(error.message)
    results.success = false
    
    return {
      success: false,
      error: error.message,
      steps: results.steps,
      errors: results.errors
    }
  }
}

// 添加快速诊断函数
window.diagnoseCache = async function() {
  console.log('🔍 开始缓存诊断...')
  
  const report = {
    browser: {
      userAgent: navigator.userAgent,
      cacheAPI: 'caches' in window,
      serviceWorker: 'serviceWorker' in navigator,
      indexedDB: 'indexedDB' in window
    },
    caches: {},
    urls: [],
    recommendations: []
  }
  
  // 检查缓存
  if (report.browser.cacheAPI) {
    try {
      const cacheNames = await caches.keys()
      console.log('发现缓存:', cacheNames)
      
      for (const cacheName of cacheNames) {
        const cache = await caches.open(cacheName)
        const keys = await cache.keys()
        report.caches[cacheName] = {
          entries: keys.length,
          urls: keys.map(req => req.url)
        }
      }
    } catch (error) {
      console.error('检查缓存时出错:', error)
    }
  }
  
  // 测试 URL
  const testUrls = ['/', '/home', '/app/dashboard']
  for (const url of testUrls) {
    try {
      const response = await fetch(url, { method: 'HEAD', cache: 'no-cache' })
      report.urls.push({
        url,
        accessible: response.ok,
        status: response.status
      })
    } catch (error) {
      report.urls.push({
        url,
        accessible: false,
        error: error.message
      })
    }
  }
  
  // 生成建议
  if (!report.browser.cacheAPI) {
    report.recommendations.push('浏览器不支持 Cache API，无法使用离线缓存功能')
  }
  
  if (!report.browser.serviceWorker) {
    report.recommendations.push('浏览器不支持 Service Worker，PWA 功能受限')
  }
  
  const failedUrls = report.urls.filter(u => !u.accessible)
  if (failedUrls.length > 0) {
    report.recommendations.push(`${failedUrls.length} 个 URL 无法访问，建议从缓存列表中移除`)
  }
  
  if (Object.keys(report.caches).length === 0) {
    report.recommendations.push('没有发现任何缓存，建议运行 fixCache() 重新初始化')
  }
  
  console.log('📊 诊断报告:', report)
  return report
}

// 添加清理函数
window.clearAllCaches = async function() {
  console.log('🧹 清理所有缓存...')
  
  try {
    const cacheNames = await caches.keys()
    const deletePromises = cacheNames.map(name => caches.delete(name))
    await Promise.all(deletePromises)
    
    console.log(`✅ 已清理 ${cacheNames.length} 个缓存:`, cacheNames)
    
    // 清理 localStorage
    const keysToRemove = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && (key.includes('cache') || key.includes('offline'))) {
        keysToRemove.push(key)
      }
    }
    
    keysToRemove.forEach(key => localStorage.removeItem(key))
    console.log(`✅ 已清理 ${keysToRemove.length} 个本地存储项`)
    
    return { success: true, clearedCaches: cacheNames.length, clearedStorage: keysToRemove.length }
  } catch (error) {
    console.error('❌ 清理缓存时出错:', error)
    return { success: false, error: error.message }
  }
}

// 显示帮助信息
console.log(`
🛠️ 缓存修复工具已加载！

可用命令：
• fixCache()      - 自动修复缓存问题
• diagnoseCache() - 诊断缓存状态
• clearAllCaches() - 清理所有缓存

在开发模式下，您还可以：
• 按 Ctrl+Shift+C 打开缓存调试面板
• 在 URL 中添加 ?cache-debug 参数自动打开调试面板
`)
