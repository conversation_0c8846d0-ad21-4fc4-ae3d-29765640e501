<template>
  <div class="cache-example">
    <h2>组件缓存示例</h2>
    
    <!-- 示例1: 基础缓存 -->
    <div class="example-section">
      <h3>1. 基础缓存（5分钟TTL）</h3>
      <p>用户信息: {{ userInfo.name }} ({{ userInfo.email }})</p>
      <p>从缓存恢复: {{ _fromCache ? '是' : '否' }}</p>
      <button @click="updateUserInfo">更新用户信息</button>
      <button @click="clearCache">清除缓存</button>
    </div>
    
    <!-- 示例2: 选择性缓存 -->
    <div class="example-section">
      <h3>2. 选择性缓存（只缓存静态数据）</h3>
      <p>静态配置: {{ staticConfig.theme }}</p>
      <p>实时数据: {{ realtimeData.timestamp }}</p>
      <p>敏感数据: {{ sensitiveData.token }}</p>
      <button @click="updateRealtimeData">更新实时数据</button>
    </div>
    
    <!-- 示例3: 版本控制缓存 -->
    <div class="example-section">
      <h3>3. 版本控制缓存</h3>
      <p>数据版本: {{ dataVersion }}</p>
      <p>产品列表: {{ products.length }} 个产品</p>
      <button @click="updateProducts">更新产品（版本+1）</button>
    </div>
    
    <!-- 缓存统计 -->
    <div class="stats-section">
      <h3>缓存统计</h3>
      <pre>{{ JSON.stringify(cacheStats, null, 2) }}</pre>
      <button @click="refreshStats">刷新统计</button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ComponentCacheExample',
  
  // 示例1: 基础缓存配置
  cache: {
    key: 'user-info-component',
    ttl: 300000, // 5分钟
  },
  
  data() {
    return {
      userInfo: {
        name: '张三',
        email: '<EMAIL>',
        lastLogin: new Date().toISOString()
      },
      staticConfig: {
        theme: 'dark',
        language: 'zh-CN'
      },
      realtimeData: {
        timestamp: Date.now(),
        onlineUsers: Math.floor(Math.random() * 1000)
      },
      sensitiveData: {
        token: 'secret-token-' + Math.random(),
        apiKey: 'api-key-12345'
      },
      products: [
        { id: 1, name: '产品A' },
        { id: 2, name: '产品B' }
      ],
      dataVersion: 1,
      cacheStats: {}
    }
  },
  
  mounted() {
    this.refreshStats()
    
    // 每5秒更新实时数据
    this.realtimeInterval = setInterval(() => {
      this.realtimeData.timestamp = Date.now()
      this.realtimeData.onlineUsers = Math.floor(Math.random() * 1000)
    }, 5000)
  },
  
  beforeDestroy() {
    if (this.realtimeInterval) {
      clearInterval(this.realtimeInterval)
    }
  },
  
  methods: {
    updateUserInfo() {
      this.userInfo.name = '李四'
      this.userInfo.email = '<EMAIL>'
      this.userInfo.lastLogin = new Date().toISOString()
    },
    
    updateRealtimeData() {
      this.realtimeData.timestamp = Date.now()
      this.realtimeData.onlineUsers = Math.floor(Math.random() * 1000)
    },
    
    updateProducts() {
      this.products.push({
        id: this.products.length + 1,
        name: `产品${String.fromCharCode(65 + this.products.length)}`
      })
      this.dataVersion++
    },
    
    refreshStats() {
      if (this.$componentCache) {
        this.cacheStats = this.$componentCache.getStats()
      }
    },
    
    // 获取数据版本（用于缓存验证）
    getDataVersion() {
      return this.dataVersion
    }
  }
}
</script>

<!-- 示例2: 选择性缓存组件 -->
<script>
// 这个组件只缓存静态配置，排除实时数据和敏感数据
const SelectiveCacheComponent = {
  name: 'SelectiveCacheComponent',
  cache: {
    key: 'selective-cache-component',
    ttl: 600000, // 10分钟
    include: ['staticConfig'], // 只缓存静态配置
    // 或者使用 exclude: ['realtimeData', 'sensitiveData']
  },
  // ... 组件定义
}
</script>

<!-- 示例3: 自定义验证缓存 -->
<script>
// 这个组件使用自定义验证逻辑
const CustomValidationComponent = {
  name: 'CustomValidationComponent',
  cache: {
    key: function() {
      // 动态缓存键，包含用户ID
      return `user-dashboard-${this.$store.state.user.id}`
    },
    ttl: 300000, // 5分钟
    validate: function(cached) {
      // 自定义验证：检查用户权限是否变化
      const currentPermissions = this.$store.state.user.permissions
      return JSON.stringify(cached.permissions) === JSON.stringify(currentPermissions)
    }
  },
  // ... 组件定义
}
</script>

<!-- 示例4: 禁用缓存 -->
<script>
// 这个组件完全禁用缓存
const NoCacheComponent = {
  name: 'NoCacheComponent',
  cache: false, // 禁用缓存
  // ... 组件定义
}
</script>

<style scoped>
.cache-example {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.example-section, .stats-section {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

.example-section h3, .stats-section h3 {
  margin-top: 0;
  color: #333;
}

button {
  margin: 5px;
  padding: 8px 16px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background: #40a9ff;
}

pre {
  background: #f0f0f0;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}
</style>
