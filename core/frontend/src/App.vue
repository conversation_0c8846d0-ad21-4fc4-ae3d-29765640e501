<template>
    <a-config-provider :locale="zh_CN">
        <div>
            <router-view>
            </router-view>
            <vue-simple-context-menu
                :elementId="'global-context-menu'"
                :options="optionsArray"
                :ref="'vueSimpleContextMenu'"
                @option-clicked="optionClicked"/>
            <AddCalendar ref="calendar" :event="calendarData" />
            <ResearchAddTag ref="researchaddtag" :research="research" />
            <CacheDebugPanel v-if="isDevelopment" />
        </div>
    </a-config-provider>
</template>

<script>
import { mapState, mapActions, mapGetters } from 'vuex'
import utils from 'utils/utils'
import moment from 'moment';
import 'utils/moment-local.js'
// import zh_CN from 'ant-design-vue/es/locale/zh_CN';
moment.locale('zh-cn');
import AddCalendar from 'components/AddCalendar'
import ResearchAddTag from 'components/ResearchAddTag'
import CacheDebugPanel from 'components/CacheDebugPanel'
import Fingerprint2 from '@fingerprintjs/fingerprintjs'
import Storage from 'utils/storage'

export default {
    name: 'app',
    components: {
        AddCalendar,
        ResearchAddTag,
        CacheDebugPanel
    },
    data() {
        return {
            calendarData: null,
            research: null,
            optionsArray: [],
            zh_CN: window.antd.locales.zh_CN,
            isDevelopment: process.env.NODE_ENV === 'development'
        }
    },
    computed:{
        ...mapState('site', ['contextmenu', 'siteConfig']),
    },
    watch: {
        contextmenu: {
            immediate: true,
            handler(val) {
                if (val && val.item) {
                    this.optionsArray = []
                    const im = val.item.important

                    if (val.type == 'news') {
                        if (this.$permission(['CALENDAR.createCalendar'])) {
                            this.optionsArray.push({
                                name: '新建到日历',
                                slug: 'news-calendar'
                            })
                        }
                        if (this.$permission(['NEWS.updateNews'])) {
                            this.optionsArray.push(
                                {
                                name: im ? '取消标记为重要' : '标记为重要（通知）',
                                slug: 'news-important'
                            })
                        }    
                    }

                    if (val.type == 'calendar' && this.$permission(['CALENDAR.createCalendar', 'CALENDAR.updateCalendar', 'EVENT.showEvent', 'EVENT.updateEvent'])) {
                        this.optionsArray = [{
                            name: '新建日历',
                            slug: 'calendar-new'
                        },
                        {
                            name: '标记为重要时间点',
                            slug: 'calendar-mark'
                        },
                        {
                            name: '标记为危险时间点',
                            slug: 'calendar-danger'
                        }]
                    }

                    if (val.type == 'media') {
                        if (this.$role('admin.top')) {
                            this.optionsArray.push({
                                name: im ? '取消标记为重要（通知）' : '标记为重要（通知）' ,
                                slug: 'media-important'
                            })
                        }
                    }

                    if (val.type == 'research') {
                        if (this.$role('admin.top')) {
                            this.optionsArray.push({
                                name: im ? '取消标记为重要（通知）' : '标记为重要（通知）' ,
                                slug: 'research-important'
                            })
                        }
                        if (this.$role('admin.top')) {
                            this.optionsArray.push({
                                name: '将研报关联到题材',
                                slug: 'research-tag'
                            })
                        }
                    }

                    if (val.type == 'stopword') {
                        if (this.$role('admin.top')) {
                            this.optionsArray.push({
                                name: '添加为停用词' ,
                                slug: 'stopword'
                            })
                        }
                    }

                    if (val.type == 'stock') {
                        if (this.$role('admin.top')) {
                            console.log(val.item);
                            this.optionsArray.push({
                                name: val.item.groupStocks && val.item.groupStocks[0] && val.item.groupStocks[0].group.id > 0 ? '取消观察' : '添加到观察',
                                slug: 'stock-group'
                            })
                        }
                    }

                    this.$refs.vueSimpleContextMenu.showMenu(this.contextmenu.event, this.contextmenu)
                }
            }
        },
    },
    async created() {
        // 您不应在页面加载时或加载后直接运行指纹。 而是使用setTimeout或requestIdleCallback将其延迟几毫秒，以确保指纹一致。
        if (window.requestIdleCallback) {
            requestIdleCallback(() => {
                this.createFingerprint();
            });
        } else {
            setTimeout(() => {
                this.createFingerprint();
            }, 500);
        }
    },
    mounted() {
        this.$store.dispatch('site/getSiteConfig')
    },
    methods: {
        createFingerprint() {
            const options = {
                // fonts: {
                //     extendedJsFonts: true,
                // },
                excludes: {
                    userAgent: false,//用户代理
                    webdriver: true,//网页内驱动软件
                    language: true,//语言种类
                    colorDepth: true,	//目标设备或缓冲器上的调色板的比特深度
                    deviceMemory: true, //设备内存
                    pixelRatio: false,//设备像素比
                    hardwareConcurrency: true,//可用于运行在用户的计算机上的线程的逻辑处理器的数量。
                    screenResolution: false,	//当前屏幕分辨率
                    availableScreenResolution: true,//屏幕宽高（空白空间）
                    timezoneOffset: true,//本地时间与 GMT 时间之间的时间差，以分钟为单位
                    timezone: false,//时区
                    sessionStorage: true,//是否会话存储
                    localStorage: true,//是否具有本地存储   
                    indexedDb: true,//是否具有索引DB
                    addBehavior: true,//IE是否指定AddBehavior
                    openDatabase: true,//是否有打开的DB
                    cpuClass: true,//浏览器系统的CPU等级
                    platform: false,//运行浏览器的操作系统和(或)硬件平台
                    doNotTrack: true,//do-not-track设置
                    plugins: true,//浏览器的插件信息
                    canvas: true,//使用 Canvas 绘图
                    webgl: true,//WebGL指纹信息
                    webglVendorAndRenderer: false,//具有大量熵的WebGL指纹的子集
                    adBlock: true,//是否安装AdBlock
                    hasLiedLanguages: true,//用户是否篡改了语言
                    hasLiedResolution: true,//用户是否篡改了屏幕分辨率
                    hasLiedOs: true,	//用户是否篡改了操作系统
                    hasLiedBrowser: true,	//用户是否篡改了浏览器
                    touchSupport: true,//触摸屏检测和能力
                    fonts: true,	//使用JS/CSS检测到的字体列表
                    fontsFlash: true,	//已安装的Flash字体列表
                    audio: true,//音频处理
                    enumerateDevices: true	//可用的多媒体输入和输出设备的信息。
                }
            };
            // 浏览器指纹
            const fingerprint = Fingerprint2.get(options, (components) => { // 参数只有回调函数时，默认浏览器指纹依据所有配置信息进行生成
                const values = components.map(component => component.value); // 配置的值的数组
                values.push(this.$store.getters['user/token'])
                // console.log('浏览器指纹', values);
                const murmur = Fingerprint2.x64hash128(values.join(''), 31); // 生成浏览器指纹
                Storage.setItem('clientId', murmur); // 存储浏览器指纹，在项目中用于校验用户身份和埋点
            });
        },
        optionClicked(event) {
            if (event.option.slug === 'news-calendar') {
                this.showCalendar(event.item.item)
            } else if (event.option.slug === 'news-important') {
                this.markNewsImportant(event.item.item, event.item.callback)
            } else if (event.option.slug === 'calendar-mark') {
                this.$store.commit('site/showLoading')
                this.$api.calendar.updateCalendar(event.item.item.format('YYYYMMDD'), {
                    type: 'mark'
                }).then((result) => {
                    event.item.callback && event.item.callback()
                }).catch((err) => {

                }).finally(() => {
                    this.$store.commit('site/hideLoading')
                })
            } else if (event.option.slug === 'calendar-danger') {
                this.$store.commit('site/showLoading')
                this.$api.calendar.updateCalendar(event.item.item.format('YYYYMMDD'), {
                    type: 'danger'
                }).then((result) => {
                    event.item.callback && event.item.callback()
                }).catch((err) => {

                }).finally(() => {
                    this.$store.commit('site/hideLoading')
                })
            } else if (event.option.slug === 'calendar-new') {
                this.showCreateCalendar(event.item.item)
            } else if (event.option.slug === 'media-important') {
                this.markMediaImportant(event.item.item)
            } else if (event.option.slug === 'research-important') {
                this.markResearchImportant(event.item.item)
            }  else if (event.option.slug === 'research-tag') {
                this.showAddTag(event.item.item)
            }  else if (event.option.slug === 'stopword') {
                this.addStopword(event.item.item)
            } else if (event.option.slug === 'stock-group') {
                this.addStockToFaGroup(event.item.item)
            }
        },
        showCalendar(item) {
            this.$refs['calendar'].show()
            this.calendarData = {
                currentTime: moment(),
                type: 'success',
                frequency: 'day',
                content: item.content,
                related: 0,
                category: 'BREAKING',
                description: '',
            };
        },
        showCreateCalendar(value) {
            this.$refs['calendar'].show()
            this.calendarData = {
                calendarId: 0,
                eventId: 0,
                currentTime: value,
                type: 'success',
                frequency: 'day',
                content: '',
                related: 0,
                category: 'BREAKING',
                description: '',
            };
        },
        showAddTag(value) {
            this.$refs['researchaddtag'].show()
            this.research = value
        }
    }
}
</script>

<style scoped lang="less">
#app {
    background: rgba(255,255,255,.2);
    color: #2c3e50;
    min-height: 100vh;
}
</style>
