# -*- coding: utf-8 -*-

"""
雪球详情分布式爬虫
基于scrapy-redis的分布式版本
爬取雪球股票相关的详细文章
数据源：xueqiu.com
"""

import time
import execjs
import json

from scrapy_redis.spiders import RedisSpider
from crawl.items import MediaItem
from crawl.tools.performance import timing_decorator
from crawl.tools.data_processor import create_news_processor
from crawl.tools.helper import clear_html, ago_day_timestr, fetch_quant_raw, get_xueqiu_cookie, filter_emoji
from scrapy.http import FormRequest


class XueqiuDetailSpider(RedisSpider):
    """
    雪球详情分布式爬虫
    从Redis队列中获取股票URL进行爬取
    """
    name = "xueqiu-detail"
    allowed_domains = ["xueqiu.com"]
    # 使用默认的Redis key格式: xueqiu-detail:start_urls

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.data_processor = create_news_processor()
        self.cookies = get_xueqiu_cookie()

        try:
            self.quant = fetch_quant_raw()
        except Exception as e:
            self.logger.warning(f"Failed to fetch quant data: {e}")
            self.quant = None

        # 初始化JS执行环境
        try:
            with open('./test.js', 'r', encoding='utf-8') as f:
                self.ctx = execjs.compile(f.read())
        except Exception as e:
            self.logger.error(f"Failed to load JS context: {e}")
            self.ctx = None

    def make_requests_from_data(self, data):
        """
        从Redis队列数据创建请求
        数据格式: JSON格式 {"url": "...", "code": "...", "data_type": "..."}
        """
        if not self.ctx:
            self.logger.error("JS context not available, cannot generate MD5")
            return

        # 解码数据
        if isinstance(data, bytes):
            data = data.decode('utf-8')

        try:
            # 尝试解析JSON数据
            import json
            url_data = json.loads(data.strip())

            url_str = url_data.get('url')
            stock_code = url_data.get('code')
            data_type = url_data.get('data_type', 'detail')

            if not url_str:
                self.logger.error(f"Invalid URL data: {url_data}")
                return None

        except json.JSONDecodeError:
            # 向后兼容：如果不是JSON格式，尝试作为简单URL处理
            url_str = data.strip()
            stock_code = self._extract_stock_code_from_url(url_str) if url_str else None
            data_type = 'detail'
            self.logger.warning(f"处理非JSON格式数据（向后兼容）: {url_str[:80]}...")

        # 调试：记录原始数据
        self.logger.debug(f"[DEBUG] 处理URL: {url_str}, 股票: {stock_code}, 类型: {data_type}")

        try:
            # 验证URL格式
            if not url_str or not url_str.startswith('https://api.xueqiu.com/query/v1/symbol/search/status.json'):
                self.logger.error(f"Invalid URL format: {url_str}")
                return None

            # 如果没有股票代码，尝试从URL中提取
            if not stock_code:
                stock_code = self._extract_stock_code_from_url(url_str)

            # 生成MD5
            md5 = self.ctx.call('getMd5', url_str)

            self.logger.info(f"[分布式爬虫] 处理股票 {stock_code}, URL: {url_str[:80]}...")

            return FormRequest(
                url=f'{url_str}&md5__1038={md5}',
                meta={'cookiejar': 1, 'url': url_str, 'stock_code': stock_code, 'data_type': data_type},
                method="GET",
                cookies=self.cookies,
                dont_filter=True,
                callback=self.parse
            )
        except Exception as e:
            self.logger.error(f"Error generating request for {url_str}: {e}")
            return None

    def make_requests_from_url(self, url):
        """
        重写默认的URL处理方法
        这个方法可能被scrapy-redis调用，而不是make_requests_from_data
        """
        self.logger.debug(f"[DEBUG] make_requests_from_url被调用: {url}")

        # 如果URL包含我们的JSON数据作为查询参数，尝试解析
        if 'stock_code=' in url and 'stock_name=' in url:
            self.logger.warning(f"检测到错误的URL格式，可能是scrapy-redis的默认处理: {url}")
            # 这种情况下，我们无法正确处理，返回None
            return None

        # 如果是正确的雪球API URL，正常处理
        if url.startswith('https://api.xueqiu.com/query/v1/symbol/search/status.json'):
            if not self.ctx:
                self.logger.error("JS context not available, cannot generate MD5")
                return None

            try:
                md5 = self.ctx.call('getMd5', url)
                self.logger.info(f"[make_requests_from_url] 处理正确的URL: {url[:80]}...")

                return FormRequest(
                    url=f'{url}&md5__1038={md5}',
                    meta={'cookiejar': 1, 'url': url},
                    method="GET",
                    cookies=self.cookies,
                    dont_filter=True,
                    callback=self.parse
                )
            except Exception as e:
                self.logger.error(f"Error in make_requests_from_url: {e}")
                return None

        # 其他情况使用默认处理
        return super().make_requests_from_url(url)

    @timing_decorator('xueqiu_detail.parse')
    def parse(self, response):
        """解析股票详情响应"""
        try:
            print(response)
            json_data = self._parse_json_response(response)
            print(json_data)
            if not json_data:
                self.logger.warning(f"Invalid response from {response.url}")
                return

            # 调试：记录API响应的基本信息
            self.logger.debug(f"API响应: count={json_data.get('count', 0)}, "
                            f"maxPage={json_data.get('maxPage', 0)}, "
                            f"list_length={len(json_data.get('list', []))}")

            # Redis分布式模式下，任务会自动从队列中移除，无需手动处理

            # 提取股票代码
            stock_code = response.meta.get('stock_code', '')

            if not stock_code:
                # 如果meta中没有，尝试从JSON数据中提取
                json_key = json_data.get('key', '')
                stock_code = json_key[2:9] if json_key else ''

            # 如果还是没有，尝试从URL中提取
            if not stock_code:
                stock_code = self._extract_stock_code_from_url(response.url)

            # 处理文章列表
            article_list = json_data.get('list', [])
            if not article_list:
                self.logger.info(f"No articles found for stock {stock_code}")
                return

            self.logger.info(f"Found {len(article_list)} articles for stock {stock_code}")

            # 时间过滤器
            time_filter = self._get_time_filter()
            self.logger.debug(f"Time filter: {time_filter}")

            valid_count = 0
            for article in article_list:
                if self._is_valid_article(article, time_filter):
                    item = self._create_article_item(article)
                    if item:
                        valid_count += 1
                        yield item

            self.logger.info(f"Processed {valid_count} valid articles out of {len(article_list)} for stock {stock_code}")

        except Exception as e:
            self._handle_error(e, response)

    def _get_time_filter(self) -> str:
        """获取时间过滤器"""
        try:
            if self.quant and hasattr(self.quant, 'stockMediaDay'):
                return ago_day_timestr(self.quant.stockMediaDay, '%Y-%m-%d')
            else:
                return ago_day_timestr(7, '%Y-%m-%d')  # 默认7天
        except Exception as e:
            self.logger.warning(f"Failed to get time filter: {e}")
            return ago_day_timestr(7, '%Y-%m-%d')

    def _is_valid_article(self, article_data: dict, time_filter: str) -> bool:
        """验证文章数据是否有效"""
        try:
            print(article_data)
            # 检查必要字段
            if not all(article_data.get(field) for field in ['id', 'title', 'created_at']):
                self.logger.debug(f"Article rejected: Missing required fields - ID: {article_data.get('id')}, Title: {article_data.get('title')}")
                return False

            # 检查时间过滤
            created_at = article_data.get('created_at', 0)
            if created_at:
                create_time = time.strftime('%Y-%m-%d', time.localtime(float(created_at / 1000)))
                if create_time < time_filter:
                    self.logger.debug(f"Article rejected: Too old - {create_time} < {time_filter}")
                    return False

            # 检查内容质量（如果有量化配置）
            if self.quant:
                content_len = len(article_data.get('text', ''))
                title_len = len(article_data.get('title', ''))
                view_count = article_data.get('view_count', 0)

                # 记录当前文章的基本信息
                self.logger.debug(f"Article stats - Content length: {content_len}, Title length: {title_len}, Views: {view_count}")

                # 内容长度检查
                if (hasattr(self.quant, 'stockMediaContent') and
                    content_len < self.quant.stockMediaContent):
                    self.logger.debug(f"Article rejected: Content too short - {content_len} < {self.quant.stockMediaContent}")
                    return False

                # 标题长度检查
                if (hasattr(self.quant, 'stockMediaTitle') and
                    title_len <= self.quant.stockMediaTitle):
                    self.logger.debug(f"Article rejected: Title too short - {title_len} <= {self.quant.stockMediaTitle}")
                    return False

                # 浏览量检查
                if (hasattr(self.quant, 'stockMediaView') and
                    view_count <= self.quant.stockMediaView):
                    self.logger.debug(f"Article rejected: Too few views - {view_count} <= {self.quant.stockMediaView}")
                    return False

            # 文章通过所有验证
            self.logger.debug(f"Article accepted: ID {article_data.get('id')}, Title: {article_data.get('title', '')[:30]}...")
            return True
        except Exception as e:
            self.logger.warning(f"Error validating article: {e}")
            return False

    def _create_article_item(self, article_data: dict) -> dict:
        """创建文章数据项"""
        try:
            # 处理时间
            created_at = article_data.get('created_at', 0)
            create_time = time.strftime('%Y-%m-%d', time.localtime(float(created_at / 1000)))

            # 创建数据项
            item = MediaItem()
            item['mediaId'] = str(article_data.get('id', ''))
            item['source'] = 'xueqiu'
            item['title'] = clear_html(article_data.get('title', ''))
            item['content'] = filter_emoji(article_data.get('text', ''))
            item['url'] = f"https://xueqiu.com/{article_data.get('user_id', '')}/{article_data.get('id', '')}"
            item['createTime'] = create_time

            # 处理数据项
            processed_item = self.process_item(dict(item))
            return processed_item

        except Exception as e:
            self.logger.error(f"Error creating article item: {e}")
            return None

    def _parse_json_response(self, response):
        """解析JSON响应"""
        try:
            import json
            return json.loads(response.text)
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON decode error: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error parsing response: {e}")
            return None

    def _handle_error(self, error, response):
        """处理错误"""
        self.logger.error(f"Error processing {response.url}: {error}")
        # 可以在这里添加错误记录逻辑

    def process_item(self, item):
        """处理数据项"""
        if self.data_processor:
            return self.data_processor.process_item(item)
        return item

    def _extract_stock_code_from_url(self, url):
        """从URL中提取股票代码"""
        try:
            import re
            # 从URL中提取symbol参数
            match = re.search(r'symbol=([^&]+)', url)
            if match:
                symbol = match.group(1)
                # 移除市场前缀（SH/SZ）
                if symbol.startswith(('SH', 'SZ')):
                    return symbol[2:]
                return symbol
            return 'unknown'
        except Exception as e:
            self.logger.warning(f"Failed to extract stock code from URL: {e}")
            return 'unknown'