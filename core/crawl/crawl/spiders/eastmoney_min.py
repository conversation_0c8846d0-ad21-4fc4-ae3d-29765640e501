# -*- coding: utf-8 -*-

"""
东方财富分时数据爬虫
爬取组合股票的分时数据
数据源：push2.eastmoney.com
"""

import hashlib
import time
import json

from crawl.items import StockMinItem
from crawl.tools.spider_base import DataSpider, SpiderConfig
from crawl.tools.performance import timing_decorator
from crawl.tools.data_processor import create_stock_processor
from crawl.tools.helper import stock_code2, ago_minutes_timestr, fetch_quant_raw
from crawl.db.db import DBSession, Source
from scrapy.http import FormRequest
from scrapy_redis.spiders import RedisSpider


class EastmoneyMinSpider(RedisSpider, DataSpider):
    """
    东方财富分时数据爬虫 - Redis分布式版本
    爬取组合股票的分时数据
    """
    name = 'eastmoney-min'
    allowed_domains = ['eastmoney.com']

    # Redis 分布式配置
    redis_key = 'eastmoney-min:start_urls'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.data_processor = create_stock_processor()

        try:
            self.quant = fetch_quant_raw()
        except Exception as e:
            self.logger.warning(f"Failed to fetch quant data: {e}")
            self.quant = None

    def get_spider_config(self) -> SpiderConfig:
        """获取爬虫配置 - 分时数据使用中等配置"""
        return SpiderConfig(
            concurrent_requests=4,
            download_delay=0.5,
            batch_size=100,
            retry_times=2
        )

    def get_start_urls(self) -> list:
        """获取起始URL列表"""
        return []  # Redis分布式模式不使用此方法

    def make_requests_from_data(self, data):
        """
        从Redis队列数据创建请求
        数据格式: JSON格式 {"url": "...", "code": "...", "data_type": "..."}
        """
        # 解码数据
        if isinstance(data, bytes):
            data = data.decode('utf-8')

        try:
            # 尝试解析JSON数据
            url_data = json.loads(data.strip())

            url_str = url_data.get('url')
            stock_code = url_data.get('code')
            data_type = url_data.get('data_type', 'min')

            if not url_str:
                self.logger.error(f"Invalid URL data: {url_data}")
                return None

        except json.JSONDecodeError:
            # 向后兼容：如果不是JSON格式，尝试作为简单URL处理
            url_str = data.strip()
            stock_code = self._extract_stock_code_from_url(url_str) if url_str else None
            data_type = 'min'
            self.logger.warning(f"处理非JSON格式数据（向后兼容）: {url_str[:80]}...")

        # 调试：记录处理的URL
        self.logger.debug(f"[分布式爬虫] 处理URL: {url_str[:80]}, 股票: {stock_code}, 类型: {data_type}")

        try:
            # 验证URL格式
            if not url_str or not url_str.startswith('http://push2.eastmoney.com/api/qt/stock/trends2/get'):
                self.logger.error(f"Invalid URL format: {url_str}")
                return None

            # 如果没有股票代码，尝试从URL中提取
            if not stock_code:
                stock_code = self._extract_stock_code_from_url(url_str)

            self.logger.info(f"[分布式爬虫] 处理股票 {stock_code}, URL: {url_str[:80]}...")

            return FormRequest(
                url=url_str,
                meta={'cookiejar': 1, 'code': stock_code, 'url': url_str, 'data_type': data_type},
                method="GET",
                callback=self.parse_response
            )
        except Exception as e:
            self.logger.error(f"Error generating request for {url_str}: {e}")
            return None

    @timing_decorator('eastmoney_min.parse_response')
    def parse_response(self, response):
        """解析分时数据响应"""
        try:
            json_data = self.parse_json_response(response)
            if not json_data or 'data' not in json_data:
                self.logger.warning(f"Invalid response from {response.url}")
                return

            data = json_data.get('data', {})
            code = data.get('code')
            pre_close = data.get('preClose', 0)
            trends = data.get('trends', [])

            if not code or not trends:
                self.logger.info(f"No trends data found for {response.meta.get('code')}")
                return

            # 时间过滤器
            time_filter = ago_minutes_timestr(5, '%Y-%m-%d %H:%M')

            # 处理分时数据
            for trend in trends:
                if self._is_valid_trend(trend, time_filter):
                    item = self._create_min_item(trend, code, pre_close)
                    if item:
                        yield item

        except Exception as e:
            self.handle_error(e, response)

    def _is_valid_trend(self, trend_data: str, time_filter: str) -> bool:
        """验证分时数据是否有效"""
        try:
            if not trend_data:
                return False

            min_items = trend_data.split(',')
            if len(min_items) < 8:
                return False

            # 检查时间过滤（可选）
            # if min_items[0] <= time_filter:
            #     return False

            return True
        except Exception:
            return False

    def _create_min_item(self, trend_data: str, code: str, pre_close: float) -> dict:
        """创建分时数据项"""
        try:
            min_items = trend_data.split(',')
            if len(min_items) < 8:
                return None

            # 生成唯一ID
            unique_id = hashlib.md5(
                (code + min_items[0]).encode("utf-8")
            ).hexdigest().lower().replace('-', '')

            # 计算涨跌幅
            current_price = float(min_items[7])
            pct_change = round((current_price - pre_close) / pre_close * 100, 2) if pre_close > 0 else 0

            # 创建数据项
            item = StockMinItem()
            item['id'] = unique_id
            item['code'] = code
            item['date'] = min_items[0]
            item['tradedate'] = min_items[0][:10]
            item['open'] = float(min_items[1]) if min_items[1] else 0
            item['close'] = float(min_items[2]) if min_items[2] else 0
            item['high'] = float(min_items[3]) if min_items[3] else 0
            item['low'] = float(min_items[4]) if min_items[4] else 0
            item['turnover'] = float(min_items[5]) if min_items[5] else 0
            item['volume'] = float(min_items[6]) if min_items[6] else 0
            item['now'] = current_price
            item['preclose'] = pre_close
            item['pctchg'] = pct_change

            # 处理数据项
            processed_item = self.process_item(dict(item))
            return processed_item

        except Exception as e:
            self.logger.error(f"Error creating min item: {e}")
            return None

    def _extract_stock_code_from_url(self, url):
        """从URL中提取股票代码"""
        try:
            import re
            # 从URL中提取secid参数
            match = re.search(r'secid=([^&]+)', url)
            if match:
                secid = match.group(1)
                # secid格式通常是 "0.000001" 或 "1.600000"
                # 提取后面的数字部分作为股票代码
                if '.' in secid:
                    return secid.split('.')[1]
                return secid
            return 'unknown'
        except Exception as e:
            self.logger.warning(f"Failed to extract stock code from URL: {e}")
            return 'unknown'
