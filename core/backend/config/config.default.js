/* eslint valid-jsdoc: "off" */

'use strict';
const path = require('path');

/**
 * 解析文件大小字符串 (如 "10mb", "1gb")
 */
function parseFileSize(sizeStr) {
  if (!sizeStr || typeof sizeStr !== 'string') {
    return null;
  }
  const units = { b: 1, kb: 1024, mb: 1024 * 1024, gb: 1024 * 1024 * 1024 };
  const match = sizeStr.toLowerCase().match(/^(\d+(?:\.\d+)?)(b|kb|mb|gb)$/);
  if (!match) {
    return parseInt(sizeStr) || null;
  }
  const [, size, unit] = match;
  return Math.floor(parseFloat(size) * units[unit]);
}

const specSequelize = require('sequelize');
const sequelizeCLSNamespace = require('cls-hooked').createNamespace('blackbear');
specSequelize.useCLS(sequelizeCLSNamespace);

const { validateConfig } = require('./config.validator');
if (process.env.NODE_ENV !== 'test') {
  try {
    validateConfig();
  } catch (error) {
    console.error('Configuration validation failed:', error.message);
    process.exit(1);
  }
}

module.exports = appInfo => {
  const config = exports = {};

  // use for cookie sign key, should change to your own and keep security
  config.keys = process.env.BB_EGG_KEYS || `${appInfo.name}_1570610257068_8645`;

  // ==================== 优化的中间件配置 ====================
  config.middleware = [
    'unifiedSecurity',
    'apmMonitor',
    // 'cacheManager',
    'responseOptimizer',
    // 'unifiedRateLimit',
    'unifiedErrorHandler',
    'userRequired',
    'cacheClear',
  ];

  // ==================== 统一限流中间件配置 ====================
  config.unifiedRateLimit = {
    enable: true,
    match: '/api',
    global: { windowMs: 15 * 60 * 1000, max: 1000 },
    userRoles: {
      admin: { max: 1000, windowMs: 15 * 60 * 1000 },
      vip: { max: 5000, windowMs: 15 * 60 * 1000 },
      premium: { max: 2000, windowMs: 15 * 60 * 1000 },
      normal: { max: 1000, windowMs: 15 * 60 * 1000 },
      guest: { max: 100, windowMs: 15 * 60 * 1000 },
    },
    endpoints: {
      'POST /api/auth/login': { max: 5, windowMs: 15 * 60 * 1000 },
      'POST /api/auth/register': { max: 3, windowMs: 60 * 60 * 1000 },
      'POST /api/auth/resetpwd': { max: 3, windowMs: 60 * 60 * 1000 },
      'GET /api/smscode': { max: 5, windowMs: 60 * 1000 },
      'POST /api/v1/upload': { max: 50, windowMs: 60 * 1000 },
      'GET /api/v1/search': { max: 100, windowMs: 60 * 1000 },
      'POST /api/v1/posts': { max: 20, windowMs: 60 * 1000 },
      'PUT /api/v1/posts/*': { max: 30, windowMs: 60 * 1000 },
      'DELETE /api/v1/posts/*': { max: 10, windowMs: 60 * 1000 },
      'POST /api/v1/cache/clear/*': { max: 5, windowMs: 60 * 1000 },
      'POST /api/v1/task': { max: 10, windowMs: 60 * 1000 },
      'GET /api/web/*': { max: 500, windowMs: 60 * 1000 },
      'GET /api/health*': { max: 1000, windowMs: 60 * 1000 },
    },
    ipWhitelist: (process.env.BB_IP_WHITELIST || '').split(',').filter(Boolean),
    ipBlacklist: (process.env.BB_IP_BLACKLIST || '').split(',').filter(Boolean),
    store: 'redis',
    keyPrefix: 'rate_limit:',
    message: 'Too many requests, please try again later',
    headers: true,
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
    skip: ctx => (
      ctx.path.startsWith('/api/health') ||
      ctx.path.includes('/ping') ||
      ctx.get('X-Internal-Request') === 'true'
    ),
  };

  // ==================== APM监控中间件配置 ====================
  config.apmMonitor = {
    enable: true,
    match: '/api',
    thresholds: {
      slow: 1000, // 慢请求阈值（毫秒）
      critical: 5000, // 严重慢请求阈值（毫秒）
      memory: 100 * 1024 * 1024, // 内存使用阈值（100MB）
    },
    sampling: {
      enabled: true,
      rate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0, // 生产环境10%采样
      slowRequestRate: 1.0, // 慢请求100%采样
    },
    metrics: {
      enabled: true,
      collectInterval: 30000, // 30秒收集一次
      retentionDays: 7, // 保留7天数据
    },
    tracing: {
      enabled: true,
      maxSpans: 100,
      includeHeaders: [ 'user-agent', 'x-forwarded-for' ],
    },
    errorTracking: {
      enabled: true,
      captureStackTrace: process.env.NODE_ENV !== 'production',
      maxStackFrames: 50,
    },
  };

  // ==================== 缓存管理中间件配置 ====================
  config.cacheManager = {
    enable: false, // 股票网站禁用API缓存，确保数据实时性
    match: '/api',
    // 排除股票相关API的缓存
    exclude: [
      '/api/stocks*',
      '/api/sources*',
      '/api/realtime*',
      '/api/market*',
      '/api/quotes*'
    ],
    warmup: {
      enabled: false, // 禁用缓存预热
      onStartup: false,
      schedule: '0 0 * * *',
    },
    autoInvalidation: {
      enabled: false, // 禁用自动失效
      patterns: {
        'user_data:*': [ 'user.update', 'user.delete' ],
        'stock_data:*': [ 'stock.update', 'stock.delete' ],
        'news_data:*': [ 'news.create', 'news.update', 'news.delete' ],
      },
    },
    monitoring: {
      enabled: true,
      slowCacheThreshold: 100, // 100ms
      reportInterval: 60000, // 1分钟
    },
    compression: {
      enabled: false, // 禁用压缩以减少延迟
      threshold: 1024,
    },
  };

  // ==================== 响应优化中间件配置 ====================
  config.responseOptimizer = {
    enable: true,
    match: '/api',
    compression: {
      enabled: true,
      threshold: 1024, // 1KB以上启用压缩
      algorithms: [ 'gzip', 'deflate', 'br' ],
      level: process.env.NODE_ENV === 'production' ? 6 : 1, // 生产环境高压缩
      chunkSize: 16 * 1024, // 16KB chunk size
    },
    cache: {
      enabled: true,
      maxAge: 300, // 5分钟
      private: false,
      mustRevalidate: false,
    },
    optimization: {
      removeEmptyFields: true,
      compactArrays: true,
      optimizeNumbers: true,
      trimStrings: true,
    },
    monitoring: {
      enabled: true,
      slowResponseThreshold: 2000, // 2秒
      largeResponseThreshold: 1024 * 1024, // 1MB
    },
    security: {
      enabled: true,
      headers: {
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
      },
    },
  };

  // ==================== bodyParser中间件配置 ====================
  config.bodyParser = {
    enable: true,
    // encoding: 'utf8',
    // formLimit: '1mb',
    // jsonLimit: '1mb',
    // strict: true,
    // queryString: {
    //   arrayLimit: 100,
    //   depth: 5,
    //   parameterLimit: 1000,
    // },
    match: ctx => !ctx.path.startsWith('/api/v1/upload'),
  };

  // ==================== CORS中间件配置 ====================
  config.cors = {
    // origin: process.env.NODE_ENV === 'production'
    //   ? (process.env.BB_CORS_ORIGINS || '').split(',').filter(Boolean)
    //   : '*',
    origin: '*',
    credentials: true,
    allowMethods: 'GET,HEAD,PUT,POST,DELETE,PATCH,OPTIONS',
    // maxAge: 86400, // 24小时
  };

  config.passportLocal = {
    usernameField: 'username',
    passwordField: 'password',
  };

  config.alinode = {
    enable: true,
    server: 'wss://agentserver.node.aliyun.com:8080',
    appid: process.env.BB_AlinodeAppid,
    secret: process.env.BB_AlinodeSecret,
    logdir: '/logs/'
  };


  config.passportWeibo = {
    key: process.env.BB_PassportWeiboKey,
    secret: process.env.BB_PassportWeiboSecret,
    callbackURL: '/passport/weibo/callback',
    proxy: true
  };

  // ==================== 静态资源中间件配置 ====================
  config.static = {
    enable: true,
    prefix: '/public/',
    dir: path.join(appInfo.baseDir, `../backend/public`),
    upload_dir: 'uploads',
    dynamic: true,
    preload: false,
    buffer: process.env.NODE_ENV === 'production',
    maxFiles: 1000,
    maxAge: process.env.NODE_ENV === 'production' ? 31536000 : 0,
  };

  // ==================== 安全配置优化 ====================
  config.security = {
    domainWhiteList: [ '*' ], // 没有配置的话，错误信息：404
    csrf: {
      ignoreJSON: true,
      headerName: 'x-csrf-token',
      enable: false, // 暂时禁用掉 csrf，错误信息：403 missing csrf token
    },
    methodnoallow: { enable: true },
    xframe: { enable: true, value: 'SAMEORIGIN' },
    hsts: { enable: process.env.NODE_ENV === 'production', maxAge: 31536000, includeSubdomains: true },
    noopen: { enable: true },
    nosniff: { enable: true },
    xssProtection: { enable: true, value: '1; mode=block' },
  };

  // ==================== 数据库配置优化 ====================
  config.sequelize = {
    dialect: 'mysql',
    host: process.env.BB_MYSQL_HOST || '127.0.0.1',
    port: parseInt(process.env.BB_MYSQL_PORT) || 3306,
    username: process.env.BB_MYSQL_USERNAME || process.env.BB_MYSQL_USER || 'root',
    password: process.env.BB_MYSQL_PASSWORD || process.env.BB_MYSQL_PASSWD || '',
    database: process.env.BB_MYSQL_DBNAME || 'blackbear',
    Sequelize: specSequelize,
    timezone: '+08:00',
    pool: {
      max: parseInt(process.env.BB_MYSQL_POOL_MAX) || 20,
      min: parseInt(process.env.BB_MYSQL_POOL_MIN) || 5,
      acquire: parseInt(process.env.BB_MYSQL_POOL_ACQUIRE) || 30000,
      idle: parseInt(process.env.BB_MYSQL_POOL_IDLE) || 10000,
      evict: parseInt(process.env.BB_MYSQL_POOL_EVICT) || 1000,
    },
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    benchmark: true,
    retry: { max: 3 },
    define: {
      timestamps: true,
      paranoid: true,
      freezeTableName: true,
      underscored: false,
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci',
    },
  };

  config.mysql = {
    // database configuration
    client: {
      // host
      host: process.env.BB_MYSQL_HOST,
      // port
      port: parseInt(process.env.BB_MYSQL_PORT) || 3306,
      // username
      user: process.env.BB_MYSQL_USER,
      // password
      password: process.env.BB_MYSQL_PASSWD,
      // database
      database: process.env.BB_MYSQL_DBNAME,
      // 连接池配置优化
      connectionLimit: parseInt(process.env.BB_MYSQL_POOL_MAX) || 20,
      acquireTimeout: parseInt(process.env.BB_MYSQL_POOL_ACQUIRE) || 30000,
      timeout: parseInt(process.env.BB_MYSQL_TIMEOUT) || 60000,
      reconnect: true,
      charset: 'utf8mb4',
      timezone: '+08:00',
      // SSL配置（生产环境建议启用）
      ssl: process.env.BB_MYSQL_SSL === 'true' ? {
        rejectUnauthorized: false,
      } : false,
    },
    // load into app, default is open
    app: true,
    // load into agent, default is close
    agent: false,
  },
  // ==================== Redis配置优化 ====================
  config.redis = {
    client: {
      host: process.env.BB_REDIS_HOST || '127.0.0.1',
      port: parseInt(process.env.BB_REDIS_PORT) || 6379,
      password: process.env.BB_REDIS_PASSWORD || '',
      db: parseInt(process.env.BB_REDIS_BACKEND_DB) || 0,
      connectTimeout: 10000,
      commandTimeout: 5000,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      // lazyConnect: true,
      family: 4,
      keyPrefix: process.env.BB_REDIS_KEY_PREFIX || 'blackbear:',
      retryDelayOnClusterDown: 300,
      enableReadyCheck: true,
      enableOfflineQueue: false,
      keepAlive: 30000,
    },
    agent: true,
  };

  // ==================== 会话配置优化 ====================
  config.session = {
    key: 'SESSION_BLACKBEAR',
    maxAge: parseInt(process.env.BB_SESSION_MAX_AGE) || 24 * 3600 * 1000,
    httpOnly: true,
    encrypt: true,
    // signed: true,
    // secure: process.env.NODE_ENV === 'production',
    // sameSite: 'lax',
    renew: true,
  };

  config.oss = {
    client: {
      accessKeyId: process.env.BB_OssAccessKeyId,
      accessKeySecret: process.env.BB_OssAccessKeySecret,
      bucket: process.env.BB_OssBucket,
      endpoint: process.env.BB_OssEndpoint,
      timeout: '60s'
    },
  };


  config.minio = {
    client: {
      endPoint: process.env.BB_MinioEndPoint,
      port: parseInt(process.env.BB_MinioPort),
      useSSL: false,
      accessKey: process.env.BB_MinioAccessKey,
      secretKey: process.env.BB_MinioSecretKey,
    },
  };

  config.wechatApi = {  
    appId: process.env.BB_WechatApiAppId,
    appSecret: process.env.BB_WechatApiAppSecret,
  };

  config.mailer = {
    host: process.env.BB_MailerHost,
    port: process.env.BB_MailerPort,
    secure: true, // true for 465, false for other ports
    auth: {
      user: process.env.BB_MailerAuthUser, // generated ethereal user
      pass: process.env.BB_MailerAuthPass,
    },
  };

  // ==================== JWT配置 ====================
  config.jwt = {
    secret: process.env.BB_JWT_SECRET,
    expiresIn: process.env.BB_JWT_EXPIRES_IN || '30d',
  };

  // ==================== 文件上传配置优化 ====================
  // 不要随意修改，eggjs error: "the multipart request can't be consumed twice",
  config.multipart = {
    mode: 'stream',
    fileSize: '100mb',
    // fileExtensions: [
    //   '.docx',
    //   '.doc',
    //   '.xls',
    //   '.xlsx',
    // ],
    whitelist: filename => true, // 不做类型限制
  };
  
  // config.multipart = {
  //   mode: 'file',
  //   autoFields: true,
  //   defaultCharset: 'utf8',
  //   fieldNameSize: 100,
  //   fieldSize: '100kb',
  //   fields: 10,
  //   fileSize: process.env.BB_UPLOAD_FILE_SIZE || '10mb',
  //   files: 10,
  //   fileExtensions: [
  //     '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp',
  //     '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
  //     '.txt', '.csv', '.json', '.xml',
  //     '.zip', '.rar', '.7z',
  //   ],
  //   whitelist: [
  //     '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp',
  //     '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
  //     '.txt', '.csv', '.json', '.xml',
  //     '.zip', '.rar', '.7z',
  //   ],
  //   // whitelist: filename => true, // 不做类型限制
  // };

  // ==================== 日志配置优化 ====================
  config.logger = {
    level: process.env.BB_LOG_LEVEL || (process.env.NODE_ENV === 'production' ? 'INFO' : 'DEBUG'),
    consoleLevel: process.env.BB_CONSOLE_LOG_LEVEL || 'INFO',
    outputJSON: process.env.NODE_ENV === 'production',
    buffer: process.env.NODE_ENV === 'production',
    appLogName: `${appInfo.name}-web.log`,
    coreLogName: 'egg-web.log',
    agentLogName: 'egg-agent.log',
    errorLogName: 'common-error.log',
    dir: './logs',
  };

  // ==================== 集群配置优化 ====================
  config.cluster = {
    listen: {
      port: parseInt(process.env.BB_PORT) || 7001,
      hostname: process.env.BB_HOSTNAME || '0.0.0.0',
    },
  };

  // ==================== 参数验证配置 ====================
  config.validate = {
    convert: true,
    validateRoot: false,
  };

  // ==================== Socket.IO配置优化 ====================
  config.io = {
    init: {
      wsEngine: 'ws',
      pingTimeout: 60000,
      pingInterval: 25000,
      transports: ['websocket', 'polling'],
    },
    namespace: {
      '/': {
        connectionMiddleware: ['connection'],
        packetMiddleware: ['packet'],
      },
    },
    redis: {
      host: process.env.BB_REDIS_HOST || '127.0.0.1',
      port: parseInt(process.env.BB_REDIS_PORT) || 6379,
      auth_pass: process.env.BB_REDIS_PASSWORD || '',
      db: parseInt(process.env.BB_REDIS_WS_DB) || 3,
    },
    generateId: req => {
      return req._query.userId;
    },
  };


  // ==================== 健康检查配置 ====================
  config.health = {
    enabled: true,
    endpoint: '/api/health',
    checks: {
      database: true,
      redis: true,
      memory: true,
      disk: true,
    },
    thresholds: {
      memory: 0.9,
      disk: 0.9,
      responseTime: 1000,
    },
  };

  // ==================== 缓存配置 ====================
  // config.cache = {
  //   default: 'redis',
  //   stores: {
  //     redis: {
  //       driver: 'redis',
  //       host: process.env.BB_REDIS_HOST || '127.0.0.1',
  //       port: parseInt(process.env.BB_REDIS_PORT) || 6379,
  //       password: process.env.BB_REDIS_PASSWORD || '',
  //       db: parseInt(process.env.BB_REDIS_CACHE_DB4) || 4,
  //     },
  //   },
  // };

  // ==================== 统一安全中间件配置 ====================
  config.unifiedSecurity = {
    enable: true,
    match: '/api',
    enableXssFilter: true,
    enableSqlInjectionFilter: true,
    enableFilenameFilter: true,
    enableUrlValidation: true,
    maxBodySize: parseFileSize(process.env.BB_UPLOAD_FILE_SIZE) || 10 * 1024 * 1024,
    filterFields: [ 'content', 'description', 'comment', 'message' ], // 移除 'text' 以允许cookie数据包含分号
    skipPaths: [ '/api/upload', '/api/file', '/api/health', '/api/v1/cookie' ], // 跳过cookie API的安全过滤
    rateLimit: {
      enabled: true,
      windowMs: 15 * 60 * 1000,
      max: 1000,
      endpoints: {
        'POST /login': { max: 5, windowMs: 15 * 60 * 1000 },
        'POST /register': { max: 3, windowMs: 60 * 60 * 1000 },
        'POST /resetpwd': { max: 3, windowMs: 60 * 60 * 1000 },
        'GET /smscode': { max: 5, windowMs: 60 * 1000 },
      },
    },
  };

  // ==================== 其它自定义配置 ====================
  const userConfig = {
    jwtExp: process.env.BB_JWT_EXPIRES_IN,
    socketOnlineUserRoomName: 'onlineUserRoom:',
    socketRedisExp: 30,
    scrapyHost: process.env.BB_SCRAPY_URL,
    taskHost: process.env.BB_TASK_API_URL,
    frontendHost: process.env.BB_FRONTEND_URL,
    aktoolsHost: process.env.BB_AKToolsHost,
    weiboPassportRedirect: process.env.BB_WeiboPassportRedirect,
    webURL: process.env.BB_WEB_URL,
    bingIndexNowKey: process.env.BB_BingIndexNowKey,
    systemUserId: process.env.BB_SystemUserId,
    adminUsername: process.env.BB_AdminUsername,
    ossHost: process.env.BB_OssHost,
    ossBucketName: process.env.BB_BB_OssBucketName,
    useOSS: process.env.BB_UseOSS,
    // 方案2缓存，股票网站禁用缓存确保实时性
    cacheEnabled: false, // 强制禁用所有服务层缓存
    app: true,
    agent: false,
  };

  return {
    ...config,
    ...userConfig,
  };
};
