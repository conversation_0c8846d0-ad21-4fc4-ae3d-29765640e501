# gocron快速优化指南

## 概述

本指南提供了可以立即实施的gocron优化方案，重点解决当前最紧迫的问题：任务状态管理和资源优化。

## 立即可实施的优化（1-3天）

### 1. 数据库连接池优化

**修改文件**: `plugin/gocron/internal/models/model.go`

```go
// 在CreateDb函数中优化连接池配置
func CreateDb() *xorm.Engine {
    dsn := getDbEngineDSN(app.Setting)
    engine, err := xorm.NewEngine(app.Setting.Db.Engine, dsn)
    if err != nil {
        logger.Fatal("创建xorm引擎失败", err)
    }
    
    // 优化连接池配置 - 立即生效
    engine.SetMaxIdleConns(15)                              // 从默认值增加到15
    engine.SetMaxOpenConns(80)                              // 从默认值增加到80
    engine.SetConnMaxLifetime(2 * time.Hour)               // 连接最大生存时间2小时
    
    // 添加连接池监控日志
    go func() {
        ticker := time.NewTicker(10 * time.Minute)
        defer ticker.Stop()
        for range ticker.C {
            stats := engine.DB().Stats()
            if stats.WaitCount > 0 {
                logger.Warn("数据库连接池等待", 
                    "等待次数", stats.WaitCount,
                    "使用中连接", stats.InUse,
                    "空闲连接", stats.Idle)
            }
        }
    }()
    
    return engine
}
```

### 2. 任务超时检测优化

**新增文件**: `plugin/gocron/internal/service/task_monitor.go`

```go
package service

import (
    "sync"
    "time"
    "github.com/ouqiang/gocron/internal/models"
    "github.com/ouqiang/gocron/internal/modules/logger"
)

var TaskMonitor = &taskMonitor{
    runningTasks: make(map[int64]*RunningTask),
}

type taskMonitor struct {
    runningTasks map[int64]*RunningTask
    mutex        sync.RWMutex
}

type RunningTask struct {
    LogID     int64
    TaskID    int
    StartTime time.Time
    Timeout   int32
    Host      string
    Port      int
}

func (tm *taskMonitor) Start() {
    go tm.monitor()
}

func (tm *taskMonitor) RegisterTask(logID int64, taskID int, timeout int32, host string, port int) {
    tm.mutex.Lock()
    defer tm.mutex.Unlock()
    
    tm.runningTasks[logID] = &RunningTask{
        LogID:     logID,
        TaskID:    taskID,
        StartTime: time.Now(),
        Timeout:   timeout,
        Host:      host,
        Port:      port,
    }
    
    logger.Info("注册运行任务", "log_id", logID, "task_id", taskID)
}

func (tm *taskMonitor) UnregisterTask(logID int64) {
    tm.mutex.Lock()
    defer tm.mutex.Unlock()
    
    delete(tm.runningTasks, logID)
    logger.Info("注销运行任务", "log_id", logID)
}

func (tm *taskMonitor) monitor() {
    ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次
    defer ticker.Stop()
    
    for range ticker.C {
        tm.checkTimeoutTasks()
    }
}

func (tm *taskMonitor) checkTimeoutTasks() {
    tm.mutex.RLock()
    timeoutTasks := make([]*RunningTask, 0)
    now := time.Now()
    
    for _, task := range tm.runningTasks {
        if task.Timeout > 0 {
            timeoutDuration := time.Duration(task.Timeout) * time.Second
            if now.Sub(task.StartTime) > timeoutDuration+30*time.Second { // 增加30秒缓冲
                timeoutTasks = append(timeoutTasks, task)
            }
        } else {
            // 对于没有设置超时的任务，如果运行超过24小时则认为异常
            if now.Sub(task.StartTime) > 24*time.Hour {
                timeoutTasks = append(timeoutTasks, task)
            }
        }
    }
    tm.mutex.RUnlock()
    
    // 处理超时任务
    for _, task := range timeoutTasks {
        tm.handleTimeoutTask(task)
    }
}

func (tm *taskMonitor) handleTimeoutTask(task *RunningTask) {
    logger.Warn("检测到超时任务", 
        "log_id", task.LogID,
        "task_id", task.TaskID,
        "运行时间", time.Since(task.StartTime),
        "设置超时", task.Timeout)
    
    // 尝试停止任务
    if task.Host != "" && task.Port > 0 {
        ServiceTask.Stop(task.Host, task.Port, task.LogID)
    }
    
    // 更新任务状态为超时
    taskLogModel := new(models.TaskLog)
    taskLogModel.Update(task.LogID, models.CommonMap{
        "status": models.Failure,
        "result": "任务执行超时，已被系统强制停止",
    })
    
    // 从监控列表中移除
    tm.UnregisterTask(task.LogID)
}
```

### 3. 修改任务执行流程

**修改文件**: `plugin/gocron/internal/service/task.go`

在`createJob`函数中添加监控：

```go
func createJob(taskModel models.Task) cron.FuncJob {
    handler := createHandler(taskModel)
    if handler == nil {
        return nil
    }
    taskFunc := func() {
        taskCount.Add()
        defer taskCount.Done()

        taskLogId := beforeExecJob(taskModel)
        if taskLogId <= 0 {
            return
        }

        if taskModel.Multi == 0 {
            runInstance.add(taskModel.Id)
            defer runInstance.done(taskModel.Id)
        }

        concurrencyQueue.Add()
        defer concurrencyQueue.Done()

        // 注册任务监控 - 新增
        if len(taskModel.Hosts) > 0 {
            host := taskModel.Hosts[0]
            TaskMonitor.RegisterTask(taskLogId, taskModel.Id, taskModel.Timeout, host.Name, host.Port)
            defer TaskMonitor.UnregisterTask(taskLogId)
        }

        logger.Infof("开始执行任务#%s#命令-%s", taskModel.Name, taskModel.Command)
        taskResult := execJob(handler, taskModel, taskLogId)
        logger.Infof("任务完成#%s#命令-%s", taskModel.Name, taskModel.Command)
        afterExecJob(taskModel, taskResult, taskLogId)
    }

    return taskFunc
}
```

### 4. 启动监控服务

**修改文件**: `plugin/gocron/internal/service/task.go`

在`Initialize`函数中启动监控：

```go
func (task Task) Initialize() {
    serviceCron = cron.New()
    serviceCron.Start()
    concurrencyQueue = ConcurrencyQueue{queue: make(chan struct{}, app.Setting.ConcurrencyQueue)}
    taskCount = TaskCount{sync.WaitGroup{}, make(chan struct{})}
    go taskCount.Wait()

    // 启动任务监控 - 新增
    TaskMonitor.Start()

    logger.Info("开始初始化定时任务")
    // ... 其余代码保持不变
}
```

### 5. 添加任务状态查询API

**新增文件**: `plugin/gocron/internal/routers/task/monitor.go`

```go
package task

import (
    "github.com/go-macaron/binding"
    "gopkg.in/macaron.v1"
    "github.com/ouqiang/gocron/internal/modules/utils"
    "github.com/ouqiang/gocron/internal/service"
    "github.com/ouqiang/gocron/internal/models"
)

// 获取运行中任务状态
func RunningTasks(ctx *macaron.Context) string {
    json := utils.JsonResponse{}
    
    // 查询数据库中状态为运行中的任务
    taskLogModel := new(models.TaskLog)
    runningTasks, err := taskLogModel.GetRunningTasks()
    if err != nil {
        return json.CommonFailure("获取运行任务失败", err)
    }
    
    // 获取监控中的任务信息
    monitorTasks := service.TaskMonitor.GetRunningTasks()
    
    result := map[string]interface{}{
        "database_running": runningTasks,
        "monitor_running":  monitorTasks,
        "total_count":      len(runningTasks),
        "monitor_count":    len(monitorTasks),
    }
    
    return json.Success("获取成功", result)
}

// 强制停止任务
func ForceStopTask(ctx *macaron.Context) string {
    json := utils.JsonResponse{}
    logID := ctx.ParamsInt64(":logId")
    
    if logID <= 0 {
        return json.CommonFailure("无效的任务日志ID", nil)
    }
    
    // 从监控中获取任务信息
    task := service.TaskMonitor.GetTask(logID)
    if task == nil {
        return json.CommonFailure("任务不在监控中", nil)
    }
    
    // 停止任务
    service.ServiceTask.Stop(task.Host, task.Port, logID)
    
    // 更新任务状态
    taskLogModel := new(models.TaskLog)
    taskLogModel.Update(logID, models.CommonMap{
        "status": models.Cancel,
        "result": "任务被手动强制停止",
    })
    
    // 从监控中移除
    service.TaskMonitor.UnregisterTask(logID)
    
    return json.Success("任务已停止", nil)
}
```

### 6. 扩展TaskLog模型

**修改文件**: `plugin/gocron/internal/models/task_log.go`

添加查询运行中任务的方法：

```go
// 获取运行中的任务
func (taskLog *TaskLog) GetRunningTasks() ([]TaskLog, error) {
    list := make([]TaskLog, 0)
    err := Db.Where("status = ?", Running).
        OrderBy("start_time DESC").
        Limit(100).
        Find(&list)
    
    return list, err
}

// 获取长时间运行的任务（超过1小时）
func (taskLog *TaskLog) GetLongRunningTasks() ([]TaskLog, error) {
    list := make([]TaskLog, 0)
    err := Db.Where("status = ? AND start_time < ?", Running, 
        time.Now().Add(-1*time.Hour)).
        OrderBy("start_time ASC").
        Find(&list)
    
    return list, err
}
```

## 配置优化

### 1. 环境变量配置

在`.env`文件中添加：

```bash
# 数据库连接池优化
DB_MAX_IDLE_CONNS=15
DB_MAX_OPEN_CONNS=80
DB_CONN_MAX_LIFETIME=7200

# 并发控制
CONCURRENCY_QUEUE=50
MAX_GOROUTINES=200

# 监控配置
TASK_MONITOR_INTERVAL=30
TASK_TIMEOUT_BUFFER=30
```

### 2. Docker配置优化

在`docker-compose.yml`中：

```yaml
services:
  gocron:
    environment:
      - DB_MAX_IDLE_CONNS=15
      - DB_MAX_OPEN_CONNS=80
      - CONCURRENCY_QUEUE=50
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'
```

## 验证效果

### 1. 监控命令

```bash
# 查看运行中的任务
curl "http://localhost:5920/api/tasks/running"

# 查看数据库连接状态
docker exec blackbear-gocron-db mysql -u root -p -e "SHOW PROCESSLIST;"

# 查看容器资源使用
docker stats blackbear-gocron
```

### 2. 日志检查

```bash
# 查看任务监控日志
docker logs blackbear-gocron | grep "任务监控\|超时任务\|连接池"

# 查看错误日志
docker logs blackbear-gocron | grep "ERROR\|WARN"
```

## 预期改善

1. **任务状态透明度**: 可以实时查看运行中任务状态
2. **超时检测**: 自动检测和处理超时任务
3. **资源优化**: 数据库连接池优化，减少连接等待
4. **运维友好**: 提供API接口查看和管理任务状态

这些优化可以立即实施，无需大规模重构，能够显著改善当前的任务状态管理问题。
