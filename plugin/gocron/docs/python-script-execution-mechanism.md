# gocron执行其他节点Python脚本的完成退出机制

## 概述

gocron是一个分布式定时任务调度系统，支持通过RPC方式在远程节点执行Python脚本。本文档详细说明了任务执行的完成退出机制，以及为什么有些脚本会一直显示"执行中"状态的原因和解决方案。

## 任务执行流程

### 1. 任务创建和调度

```go
func createJob(taskModel models.Task) cron.FuncJob {
    handler := createHandler(taskModel)
    taskFunc := func() {
        taskCount.Add()
        defer taskCount.Done()

        taskLogId := beforeExecJob(taskModel)  // 创建任务日志，状态设为Running
        if taskLogId <= 0 {
            return
        }

        logger.Infof("开始执行任务#%s#命令-%s", taskModel.Name, taskModel.Command)
        taskResult := execJob(handler, taskModel, taskLogId)  // 执行任务
        logger.Infof("任务完成#%s#命令-%s", taskModel.Name, taskModel.Command)
        afterExecJob(taskModel, taskResult, taskLogId)  // 更新任务状态
    }
    return taskFunc
}
```

### 2. RPC远程执行

对于执行其他节点的Python脚本，gocron使用RPC协议：

```go
func (h *RPCHandler) Run(taskModel models.Task, taskUniqueId int64) (result string, err error) {
    taskRequest := new(pb.TaskRequest)
    taskRequest.Timeout = int32(taskModel.Timeout)
    taskRequest.Command = taskModel.Command
    taskRequest.Id = taskUniqueId
    
    // 并发执行多个主机上的任务
    for _, taskHost := range taskModel.Hosts {
        go func(th models.TaskHostDetail) {
            output, err := rpcClient.Exec(th.Name, th.Port, taskRequest)
            // 处理执行结果
        }(taskHost)
    }
}
```

## 任务状态管理

### 状态定义

```go
const (
    Disabled Status = 0 // 禁用
    Failure  Status = 0 // 失败
    Enabled  Status = 1 // 启用
    Running  Status = 1 // 运行中
    Finish   Status = 2 // 完成
    Cancel   Status = 3 // 取消
)
```

### 状态流转

1. **任务开始**: 状态设置为`Running`(1)
2. **任务完成**: 根据执行结果设置为`Finish`(2)或`Failure`(0)
3. **任务取消**: 手动停止或超时后设置为`Cancel`(3)

### 状态更新机制

```go
func updateTaskLog(taskLogId int64, taskResult TaskResult) (int64, error) {
    taskLogModel := new(models.TaskLog)
    var status models.Status
    result := taskResult.Result
    if taskResult.Err != nil {
        status = models.Failure  // 执行失败
    } else {
        status = models.Finish   // 执行成功
    }
    return taskLogModel.Update(taskLogId, models.CommonMap{
        "retry_times": taskResult.RetryTimes,
        "status":      status,
        "result":      result,
    })
}
```

## 超时和完成检查机制

### 1. RPC客户端超时控制

```go
func Exec(ip string, port int, taskReq *pb.TaskRequest) (string, error) {
    // 设置超时时间，默认最大86400秒(24小时)
    if taskReq.Timeout <= 0 || taskReq.Timeout > 86400 {
        taskReq.Timeout = 86400
    }
    timeout := time.Duration(taskReq.Timeout) * time.Second
    ctx, cancel := context.WithTimeout(context.Background(), timeout)
    defer cancel()

    // 存储取消函数，用于手动停止任务
    taskUniqueKey := generateTaskUniqueKey(ip, port, taskReq.Id)
    taskMap.Store(taskUniqueKey, cancel)
    defer taskMap.Delete(taskUniqueKey)

    resp, err := c.Run(ctx, taskReq)
    if err != nil {
        return parseGRPCError(err)
    }
}
```

### 2. Shell命令执行控制

```go
func ExecShell(ctx context.Context, command string) (string, error) {
    cmd := exec.Command("/bin/bash", "-c", command)
    cmd.SysProcAttr = &syscall.SysProcAttr{
        Setpgid: true,  // 创建新的进程组
    }
    
    resultChan := make(chan Result)
    go func() {
        output, err := cmd.CombinedOutput()
        resultChan <- Result{string(output), err}
    }()
    
    select {
    case <-ctx.Done():
        // 超时或被取消，强制杀死进程组
        if cmd.Process.Pid > 0 {
            syscall.Kill(-cmd.Process.Pid, syscall.SIGKILL)
        }
        return "", errors.New("timeout killed")
    case result := <-resultChan:
        return result.output, result.err
    }
}
```

### 3. 错误处理

```go
func parseGRPCError(err error) (string, error) {
    switch status.Code(err) {
    case codes.Unavailable:
        return "", errUnavailable
    case codes.DeadlineExceeded:
        return "", errors.New("执行超时, 强制结束")
    case codes.Canceled:
        return "", errors.New("手动停止")
    }
    return "", err
}
```

## 脚本一直显示"执行中"的原因分析

### 1. 超时设置问题

**原因**: 
- 任务的`Timeout`设置为0（无限制）或设置过大
- 默认最大超时时间是86400秒（24小时）

**解决方案**:
```sql
-- 检查任务超时设置
SELECT id, name, timeout, command FROM task WHERE timeout = 0 OR timeout > 3600;

-- 更新超时设置（例如设置为30分钟）
UPDATE task SET timeout = 1800 WHERE id = <task_id>;
```

### 2. Python脚本问题

**常见问题**:
- 脚本进入死循环
- 等待用户输入
- 长时间的网络请求或数据处理
- 脚本异常但没有正确退出

**解决方案**:
```python
# 在Python脚本中添加超时控制
import signal
import sys

def timeout_handler(signum, frame):
    print("脚本执行超时，强制退出")
    sys.exit(1)

# 设置信号处理器
signal.signal(signal.SIGALRM, timeout_handler)
signal.alarm(1800)  # 30分钟超时

try:
    # 你的脚本逻辑
    main_logic()
finally:
    signal.alarm(0)  # 取消超时
```

### 3. 进程管理问题

**原因**:
- Python脚本创建了子进程但没有正确管理
- 父进程退出但子进程仍在运行

**解决方案**:
```python
import subprocess
import os
import signal

def cleanup_processes():
    """清理所有子进程"""
    try:
        # 获取当前进程组ID
        pgid = os.getpgid(0)
        # 终止整个进程组
        os.killpg(pgid, signal.SIGTERM)
    except:
        pass

# 注册退出处理器
import atexit
atexit.register(cleanup_processes)
```

### 4. 网络连接问题

**原因**:
- RPC连接中断，但任务状态没有及时更新
- 目标节点宕机或网络不通

**检查方法**:
```bash
# 检查网络连接
telnet <target_host> <rpc_port>

# 检查gocron-node服务状态
systemctl status gocron-node

# 查看网络连接
netstat -an | grep <rpc_port>
```

### 5. 数据库状态不一致

**原因**:
- 任务执行完成但状态更新失败
- 数据库连接问题

**检查方法**:
```sql
-- 查看长时间运行的任务
SELECT 
    id, task_id, name, status, start_time,
    TIMESTAMPDIFF(MINUTE, start_time, NOW()) as running_minutes
FROM task_log 
WHERE status = 1 
AND start_time < DATE_SUB(NOW(), INTERVAL 1 HOUR)
ORDER BY start_time;
```

## 监控和故障排除

### 1. 日志监控

```bash
# 查看gocron主服务日志
docker logs -f blackbear-gocron

# 查看特定任务的执行日志
grep "任务id-<task_id>" /app/log/cron.log

# 查看RPC调用日志
grep "execute cmd" /app/log/cron.log
```

### 2. 系统资源监控

```bash
# 检查目标节点CPU和内存使用
top -p $(pgrep python)

# 检查磁盘空间
df -h

# 检查网络连接数
ss -an | grep <rpc_port> | wc -l
```

### 3. 手动停止任务

```go
// 通过API停止任务
func (task Task) Stop(ip string, port int, id int64) {
    rpcClient.Stop(ip, port, id)
}
```

或通过Web界面的"停止"按钮强制终止运行中的任务。

## 最佳实践

### 1. 任务配置

- 设置合理的超时时间（建议不超过1小时）
- 启用任务重试机制，但限制重试次数
- 配置任务执行结果通知

### 2. Python脚本编写

- 添加适当的日志输出
- 实现优雅的退出机制
- 处理信号和异常
- 避免无限循环和阻塞操作

### 3. 监控告警

- 监控长时间运行的任务
- 设置任务执行时间告警
- 监控系统资源使用情况

### 4. 定期维护

- 清理历史任务日志
- 检查和更新任务配置
- 监控节点健康状态

## 故障排除流程图

```mermaid
graph TD
    A[任务显示执行中] --> B{检查执行时间}
    B -->|超过预期时间| C[检查超时设置]
    B -->|正常范围内| D[等待或检查脚本逻辑]

    C --> E{超时设置是否合理}
    E -->|设置过大或为0| F[调整超时时间]
    E -->|设置合理| G[检查脚本状态]

    G --> H{脚本是否响应}
    H -->|无响应| I[强制停止任务]
    H -->|有响应| J[检查脚本逻辑]

    I --> K[清理残留进程]
    J --> L[优化脚本代码]

    F --> M[重新执行任务]
    K --> M
    L --> M
```

## 配置示例

### 1. 任务配置示例

```json
{
  "name": "数据处理脚本",
  "protocol": 2,
  "command": "cd /app/scripts && python3 data_processor.py --config prod.json",
  "timeout": 1800,
  "retry_times": 2,
  "retry_interval": 300,
  "hosts": [
    {
      "name": "*************",
      "port": 5921,
      "alias": "数据节点1"
    }
  ]
}
```

### 2. Python脚本模板

```python
#!/usr/bin/env python3
"""
gocron执行的Python脚本模板
包含超时控制、日志记录、异常处理等最佳实践
"""

import sys
import os
import signal
import logging
import time
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('/tmp/script.log')
    ]
)
logger = logging.getLogger(__name__)

class ScriptTimeout(Exception):
    """脚本超时异常"""
    pass

def timeout_handler(signum, frame):
    """超时信号处理器"""
    logger.error("脚本执行超时，强制退出")
    raise ScriptTimeout("脚本执行超时")

def cleanup():
    """清理函数"""
    logger.info("执行清理操作")
    # 清理临时文件、关闭连接等

def main():
    """主要业务逻辑"""
    try:
        # 设置超时处理
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(1800)  # 30分钟超时

        logger.info("脚本开始执行")
        start_time = time.time()

        # 你的业务逻辑
        # process_data()

        end_time = time.time()
        logger.info(f"脚本执行完成，耗时: {end_time - start_time:.2f}秒")

        return 0

    except ScriptTimeout:
        logger.error("脚本执行超时")
        return 1
    except Exception as e:
        logger.error(f"脚本执行异常: {str(e)}")
        return 1
    finally:
        signal.alarm(0)  # 取消超时
        cleanup()

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
```

### 3. 监控脚本

```bash
#!/bin/bash
# gocron任务监控脚本

MYSQL_HOST="localhost"
MYSQL_USER="root"
MYSQL_PASS="password"
MYSQL_DB="gocron"

# 检查长时间运行的任务
check_long_running_tasks() {
    echo "=== 检查长时间运行的任务 ==="
    mysql -h$MYSQL_HOST -u$MYSQL_USER -p$MYSQL_PASS $MYSQL_DB -e "
    SELECT
        tl.id,
        tl.task_id,
        tl.name,
        tl.start_time,
        TIMESTAMPDIFF(MINUTE, tl.start_time, NOW()) as running_minutes,
        tl.command
    FROM task_log tl
    WHERE tl.status = 1
    AND tl.start_time < DATE_SUB(NOW(), INTERVAL 30 MINUTE)
    ORDER BY tl.start_time;
    "
}

# 检查失败的任务
check_failed_tasks() {
    echo "=== 最近1小时失败的任务 ==="
    mysql -h$MYSQL_HOST -u$MYSQL_USER -p$MYSQL_PASS $MYSQL_DB -e "
    SELECT
        tl.id,
        tl.task_id,
        tl.name,
        tl.start_time,
        tl.end_time,
        LEFT(tl.result, 200) as error_msg
    FROM task_log tl
    WHERE tl.status = 0
    AND tl.start_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)
    ORDER BY tl.start_time DESC;
    "
}

# 检查系统资源
check_system_resources() {
    echo "=== 系统资源使用情况 ==="
    echo "CPU使用率:"
    top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1

    echo "内存使用率:"
    free | grep Mem | awk '{printf "%.2f%%\n", $3/$2 * 100.0}'

    echo "磁盘使用率:"
    df -h | grep -E '^/dev/' | awk '{print $5 " " $6}'
}

# 主函数
main() {
    echo "gocron任务监控报告 - $(date)"
    echo "================================"

    check_long_running_tasks
    echo
    check_failed_tasks
    echo
    check_system_resources
}

main
```

## 总结

gocron的任务完成退出机制依赖于：
1. **RPC超时控制**: 通过context.WithTimeout实现
2. **进程组管理**: 使用syscall.Kill强制终止进程组
3. **数据库状态更新**: 及时更新任务执行状态
4. **错误处理和重试机制**: 自动重试失败的任务

当脚本一直显示"执行中"时，需要从以下方面进行排查：
- 检查超时设置是否合理
- 分析Python脚本逻辑是否存在问题
- 验证网络连接和RPC服务状态
- 监控系统资源使用情况
- 检查数据库状态更新机制

通过合理的配置、规范的脚本编写和有效的监控，可以大大减少任务执行异常的情况。
